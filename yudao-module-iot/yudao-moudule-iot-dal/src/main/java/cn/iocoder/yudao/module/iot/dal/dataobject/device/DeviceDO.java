package cn.iocoder.yudao.module.iot.dal.dataobject.device;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Document("devices")
@NoArgsConstructor
@AllArgsConstructor
public class DeviceDO implements Serializable {
    public static final String COLLECTION_NAME = "devices";
    public static final long serialVersionUID = 1L;
    public static final long DEVICE_DEFAULT_PROJECT_ID = 0L;
    public static final String COL_ID = "_id";
    public static final String COL_PRODUCT_ID = "productId";
    public static final String COL_PRODUCT_KEY = "productKey";
    public static final String COL_SN = "sn";
    public static final String COL_NAME = "name";
    public static final String COL_OWNER = "owner";
    public static final String COL_MOBILE = "mobile";
    public static final String COL_PROVINCE = "province";
    public static final String COL_CITY = "city";
    public static final String COL_DIVISION = "division";
    public static final String COL_IS_GATEWAY = "isGateway";
    public static final String COL_DESCRIPTION = "description";
    public static final String COL_AREA_CODE = "areaCode";
    public static final String COL_TOWN = "town";
    public static final String COL_LOCATION = "location";
    public static final String COL_IP = "ip";
    public static final String COL_STATUS = "status";
    public static final String COL_BATCH_ID = "batchId";
    public static final String COL_ONLINE_TIME = "onlineTime";
    public static final String COL_OFFLINE_TIME = "offlineTime";
    public static final String COL_CONFIGS = "configs";
    public static final String COL_ACTIVATE_TIME = "activateTime";
    public static final String COL_DEPT_ID = "deptId";
    public static final String COL_MANUFACTURER = "manufacturer";
    public static final String COL_FW_VERSION = "fwVersion";
    public static final String COL_HD_VERSION = "hdVersion";
    public static final String COL_MAC = "mac";
    public static final String COL_SECRET = "secret";
    public static final String COL_CREATE_TIME = "createTime";
    public static final String COL_UPDATE_TIME = "updateTime";
    public static final String COL_CREATOR = "creator";
    public static final String COL_UPDATER = "updater";
    public static final String COL_NODE_ID = "nodeId";
    public static final String COL_TENANT_ID = "tenantId";
    public static final String COL_PROJECT_ID = "projectId";
    public static final String COL_UNIQ_CODE = "uniqCode";
    public static final String COL_IMEI = "imei";
    public static final String COL_HAS_REGjiISTERED = "hasRegistered";
    public static final String COL_FIRST_BIND_TIME = "firstBindTime";
    public static final String COL_BIND_TIME = "bindTime";
    public static final String COL_UNBIND_TIME = "unBindTime";

    public static final String COL_HCDN_BATCH_ID = "hcdnBatchId";
    public static final String COL_FROM="from";
    public static final Integer FROM_OTA=1;
    public static final Integer FROM_ROUTER_APP=2;
    /**
     * ID
     */
    @Id
    private Long id;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 产品Key
     */
    private String productKey;
    /**
     * 设备SN
     */
    private String sn;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 拥有者
     */
    private String owner;
    private String province;
    private String city;
    private String division;
    /**
     * 是否是网关
     */
    private Integer isGateway;
    /**
     * 备注
     */
    private String description;
    /**
     * 省码
     */
    private String areaCode;

    /**
     * 镇
     */
    private String town;
    /**
     * 所属区域
     */
    private String location;
    /**
     * ip信息
     */
    private String ip;

    private String snCode;
    /**
     * 状态 0: 禁用 1:未激活 2:离线 3:在线
     * 在线的设备都是激活，激活的设备才能是在线
     * <p>
     * 枚举 {@link cn.iocoder.yudao.module.iot.enums.device.DeviceStatusEnum}
     */
    private Integer status;
    /**
     * 批次ID
     */
    private Long batchId;
    /**
     * 最后上线时间
     */
    private Date onlineTime;
    /**
     * 最后离线时间
     */
    private Date offlineTime;
    /**
     * 配置
     */
    private Map<String, Object> configs;
    /**
     * 激活时间
     */
    private Date activateTime;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 厂商编码
     */
    private String manufacturer;
    /**
     * 固件版本
     */
    private String fwVersion;
    /**
     * 硬件版本
     */
    private String hdVersion;

    private String mac;

    private String secret;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     * <p>
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     * <p>
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updater;

    /**
     * 节点Id
     */
    private Long nodeId;
    /**
     * tenantId
     */
    private Long tenantId;

    /**
     * 项目ID
     */
    private Long projectId;

    private String uniqCode;

    /**
     * imei信息
     */
    private String imei;
    private String cmei;

    private String sdkVersion;

    private String platform;
    /**
     * 是否动态注册过
     */
    private Boolean hasRegistered;
    /**
     * 第一次绑定时间
     */
    private Date firstBindTime;
    private Date bindTime;
    /**
     * 解绑时间
     */
    private Date unBindTime;

    private Long hcdnBatchId;

    private String mobile;

    private Integer from;

    @Transient
    private Boolean sharedDevice = false;
}
