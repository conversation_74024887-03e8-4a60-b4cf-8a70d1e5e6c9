package cn.iocoder.yudao.module.iot.controller.open.hw.conversation.service;

import cn.iocoder.yudao.module.iot.controller.open.hw.conversation.domain.Message;

import java.util.List;

public interface LLMService {
    // 普通响应方法
    String getResponse(List<Message> messages);
    
    // 流式响应方法
    void getStreamResponse(String systemPrompt,List<Message> messages,StreamStatus streamStatus, LLMStreamCallback callback);

    List<String> getSuggestions(List<Message>messages);

    String getSummary(List<Message> messages);
}

// 回调接口

