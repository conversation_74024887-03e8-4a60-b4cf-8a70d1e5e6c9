package cn.iocoder.yudao.module.iot.mrg.service.device;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.iocoder.yudao.framework.common.util.date.DateUtils;
import cn.iocoder.yudao.module.iot.dal.dataobject.device.DeviceDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.device.SubDeviceDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.devicerunteime.DeviceRunTimeDO;
import cn.iocoder.yudao.module.iot.dal.mongo.DeviceMapper;
import cn.iocoder.yudao.module.iot.dal.mongo.DeviceRepository;
import cn.iocoder.yudao.module.iot.dal.mongo.ReactiveDeviceRepository;
import cn.iocoder.yudao.module.iot.dal.mongo.SubDeviceRepository;
import cn.iocoder.yudao.module.iot.dal.tdengine.deviceruntime.DeviceRuntimeMapper;
import cn.iocoder.yudao.module.iot.mrg.config.MrgProperties;
import cn.iocoder.yudao.module.iot.mrg.controller.device.vo.DeviceRuntimeReqVO;
import cn.iocoder.yudao.module.iot.mrg.controller.device.vo.SubDeviceRuntimeVO;
import cn.iocoder.yudao.module.iot.mrg.dto.Location;
import cn.iocoder.yudao.module.iot.mrg.service.dict.DictService;
import cn.iocoder.yudao.module.iot.mrg.util.IPRegionUtil;
import cn.iocoder.yudao.module.iot.mrg.util.ProductUtils;
import cn.iocoder.yudao.module.iot.mrg.util.RedissonOption;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.github.yitter.idgen.YitIdHelper;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLocalCachedMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.codec.CompositeCodec;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.module.iot.dal.redis.RedisKeyConstants.DEVICE_LOCAL_CACHE_MAP;
import static cn.iocoder.yudao.module.iot.dal.redis.RedisKeyConstants.DEVICE_MRG_LOCAL_CACHE_MAP;

@Slf4j
@Service
public class DeviceService {
    private final DeviceMapper deviceMapper;
    private final DeviceRepository deviceRepository;
    private final ReactiveDeviceRepository reactiveDeviceRepository;

    public static RLocalCachedMap<String, DeviceDO> deviceLocalCacheMap;
    private static volatile Map<Long, DeviceDO> deviceUpdateCache = new ConcurrentHashMap<>(4096);

    private volatile static Map<String, String> provinceCodeCache = new HashMap<>();
    private final DictService dictService;
    private final SubDeviceRepository subDeviceRepository;
    private final DeviceRuntimeMapper deviceRuntimeMapper;
    private final RedissonOption redissonOption;
    private final MrgProperties mrgProperties;
    private static final Integer MAX_CACHE_SIZE = 40960;
    private static final CopyOptions COPY_OPTIONS = new CopyOptions().ignoreNullValue();

    @PostConstruct
    public void initManuProvinceCode() {
        List<DictDataDO> dictDatas = dictService.findByDictType("iot_device_area_code");
        Map<String, String> map = dictDatas.stream().collect(Collectors.toMap(DictDataDO::getValue, DictDataDO::getLabel));
        provinceCodeCache = map;
        log.info("init ProvinceCode {}", provinceCodeCache.size());
    }

    public void save(DeviceDO deviceDO) {
        if (deviceDO.getId() == null) {
            deviceDO.setId(YitIdHelper.nextId());
        }
        deviceLocalCacheMap.fastRemove(ProductUtils.deviceIdCombine(deviceDO.getProductKey(), deviceDO.getSn()));
        deviceRepository.save(deviceDO);
    }

    public String getProvinceByCode(String code) {
        if (code == null) {
            return null;
        }
        String province = provinceCodeCache.get(code);
        if (province == null) {
            return code;
        }
        return province;
    }

    @Scheduled(fixedDelay = 60000, initialDelay = 60000)
    public void schedulePeriodicRefresh() {
        initManuProvinceCode();
    }

    public void clearDeviceCache(String productKey, String sn) {
        deviceLocalCacheMap.fastRemoveAsync(ProductUtils.deviceIdCombine(productKey, sn));
    }

    public void clearDeviceCacheBatch(List<DeviceDO> deviceList) {
       List<String> keyList = deviceList.stream().map(deviceDO ->ProductUtils.deviceIdCombine(deviceDO.getProductKey(), deviceDO.getSn()))
                        .collect(Collectors.toList());
        deviceLocalCacheMap.fastRemoveAsync(keyList.toArray(new String[keyList.size()]));
    }


    public DeviceService(DeviceMapper deviceMapper, DeviceRepository deviceRepository, RedissonClient redissonClient,
                         DictService dictService, SubDeviceRepository subDeviceRepository,
                         DeviceRuntimeMapper deviceRuntimeMapper, RedissonOption redissonOption, MrgProperties mrgProperties, ReactiveDeviceRepository reactiveDeviceRepository) {
        this.deviceMapper = deviceMapper;
        this.deviceRepository = deviceRepository;
        this.dictService = dictService;
        this.mrgProperties = mrgProperties;
        this.reactiveDeviceRepository = reactiveDeviceRepository;
        JsonJacksonCodec jsonJacksonCodec = new JsonJacksonCodec();
        jsonJacksonCodec.getObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);
        if (mrgProperties.getIsMrg()) {
            deviceLocalCacheMap = redissonClient.getLocalCachedMap(DEVICE_MRG_LOCAL_CACHE_MAP, new CompositeCodec(new StringCodec(), jsonJacksonCodec), redissonOption.getMrgCacheOps());
        } else {
            deviceLocalCacheMap = redissonClient.getLocalCachedMap(DEVICE_LOCAL_CACHE_MAP, new CompositeCodec(new StringCodec(), jsonJacksonCodec), redissonOption.getDeviceCacheOps());
        }
        this.subDeviceRepository = subDeviceRepository;
        this.deviceRuntimeMapper = deviceRuntimeMapper;
        this.redissonOption = redissonOption;
    }

    public void update(DeviceDO deviceDO) {
        deviceMapper.updateById(deviceDO);
        deviceLocalCacheMap.fastRemove(ProductUtils.deviceIdCombine(deviceDO.getProductKey(), deviceDO.getSn()));
    }

    /**
     * 设备恢复出厂设置
     *
     * @param deviceDO
     */
    public void restoreFactorySettings(DeviceDO deviceDO) {
        deviceDO.setOwner(null);
        deviceDO.setMobile(null);
        deviceDO.setUpdateTime(new Date());
        deviceRepository.save(deviceDO);
        deviceLocalCacheMap.fastRemove(ProductUtils.deviceIdCombine(deviceDO.getProductKey(), deviceDO.getSn()));
    }

    public void updateOnline(DeviceDO deviceDO) {
        if (deviceUpdateCache.get(deviceDO.getId()) != null) {
            DeviceDO tmp = deviceUpdateCache.get(deviceDO.getId());
            BeanUtil.copyProperties(deviceDO, tmp, COPY_OPTIONS);
            deviceDO = tmp;
        }

        log.info("更新设备{} {}", deviceDO.getSn(), JSON.toJSONString(deviceDO));
        if (deviceDO.getIp() != null) {
            if (deviceDO.getOwner() == null || StringUtil.isBlank(deviceDO.getProvince())) {
                Location location = IPRegionUtil.getLocationInfo(deviceDO.getIp());
                if (StringUtil.isNotEmpty(location.getLocation()) && !"0".equals(location.getLocation())) {
                    deviceDO.setProvince(location.getProvince());
                    deviceDO.setCity(location.getCity());
                    deviceDO.setDivision(location.getDivision());
                    deviceDO.setLocation(location.getLocation());
                    log.info("[IP={},归属地{}]", deviceDO.getIp(), location.getLocation());
                }
            }
        }
        if (deviceUpdateCache.size() < MAX_CACHE_SIZE) {
            deviceUpdateCache.put(deviceDO.getId(), deviceDO);
        } else {
            log.warn("deviceUpdateCache is full {},ignore", deviceUpdateCache.size());
        }
    }

    public Mono<DeviceDO> findReactive(String productKey, String sn) {
        final String deviceUniqKey = ProductUtils.deviceIdCombine(productKey, sn);
        DeviceDO device = deviceLocalCacheMap.get(deviceUniqKey);
        if (device == null) {
            log.info("[productKey={},deviceSn={}]", productKey, sn);
            return reactiveDeviceRepository.findFirstByProductKeyAndSn(productKey, sn)
                    .flatMap(deviceDO -> {
                        if(deviceDO!= null){
                            log.info("find device {} from db", deviceUniqKey);
                            deviceLocalCacheMap.fastPut(deviceUniqKey, deviceDO);
                            log.info("fastPut device {} toCache", deviceUniqKey);
                        }else{
                            log.info("device {} not exist in db",deviceUniqKey);
                        }
                        return Mono.just(deviceDO);
                    });
        } else {
            return Mono.just(device);
        }
    }


    /**
     * <p>
     * 1. first find from  local cache <br>
     * 2. find from redis if local cache not exist <br>
     * 3. find from db if redis not exist <br>
     * </p>
     *
     * @param productKey
     * @param sn
     * @return device obj
     */

    public DeviceDO find(String productKey, String sn) {
        String deviceUniqKey = ProductUtils.deviceIdCombine(productKey, sn);
        DeviceDO device = deviceLocalCacheMap.get(deviceUniqKey);
        if (device == null) {
            log.info("[productKey={},deviceSn={}]", productKey, sn);
            device = deviceMapper.selectByProductKeyAndSn(productKey,sn);
            log.info("find device {} from db", deviceUniqKey);
            if (device.getId() == null) {
                log.info("device {} not in db!", deviceUniqKey);
            }else{
                deviceLocalCacheMap.fastPut(deviceUniqKey, device);
            }
        }
        return device;
    }

    public DeviceDO findBySnCode(String productKey, String snCode) {
        String deviceUniqKey = ProductUtils.deviceIdCombine(productKey, snCode);
        DeviceDO device = deviceLocalCacheMap.get(deviceUniqKey);
        if (device == null) {
            log.info("[productKey={},deviceSnCode={}]", productKey, snCode);
            device = deviceMapper.selectByProductKeyAndSnCode(productKey,snCode);
            log.info("find device {} from db", deviceUniqKey);
            if (device.getId() == null) {
                log.info("device {} not in db!", deviceUniqKey);
            }else{
                deviceLocalCacheMap.fastPut(deviceUniqKey, device);
            }
        }
        return device;
    }

    public DeviceDO findById(Long id) {
        return deviceMapper.selectById(id);
    }

    public DeviceDO findBySn(String sn) {
        return deviceMapper.selectBySn(sn);
    }

    public DeviceDO findByMac(String mac) {
        return deviceMapper.selectByMac(mac);
    }

    @Scheduled(fixedDelayString = "${iot.mrg.device-update-delay:200}", initialDelayString = "${iot.mrg.device-update-delay:200}")
    public void schedule() {
        flushDeviceUpdateCache();
    }

    public void flushDeviceUpdateCache() {
        if (!deviceUpdateCache.isEmpty()) {
            log.info("update device cache {}", deviceUpdateCache.size());
            Map<Long, DeviceDO> tmp = deviceUpdateCache;
            Collection<DeviceDO> deviceUpdateList = tmp.values();
            deviceUpdateCache = new ConcurrentHashMap<Long, DeviceDO>(4096);
            deviceMapper.batchUpdate(deviceUpdateList);
            List<Long> ids = deviceUpdateList.stream().map(DeviceDO::getId).collect(Collectors.toList());
            Map<String, DeviceDO> map = deviceMapper.selectBatchIds(ids)
                    .stream().collect(Collectors.toMap(deviceDO -> ProductUtils.deviceIdCombine(deviceDO.getProductKey(), deviceDO.getSn()), deviceDO -> deviceDO));
            if (!map.isEmpty()) {
                deviceLocalCacheMap.putAll(map);
            }
            log.info("批量更新设备{}", deviceUpdateList.size());
        }
    }

    @PreDestroy
    public void preDestroy() {
        log.info("receive destory event,start flush deviceUpdateCache {}", deviceUpdateCache.size());
        flushDeviceUpdateCache();
    }

    public void reportRunTime(Long deviceId, DeviceRuntimeReqVO reqVO) {
        Long yesterdayTime = System.currentTimeMillis() - DateUtils.ONE_DAY_MILL_SECONDS;
        Integer yesterday = Integer.parseInt(DateUtils.getCurrentDate(yesterdayTime));
        List<SubDeviceRuntimeVO> subDeviceRuntimeVOList = reqVO.getRuntimeList();
        Map<String, SubDeviceRuntimeVO> map = subDeviceRuntimeVOList.stream().collect(Collectors.toMap(SubDeviceRuntimeVO::getMac, Function.identity()));
        List<SubDeviceDO> subDeviceList = subDeviceRepository.findAllByDeviceIdAndMacIn(deviceId, map.keySet());
        Set<String> existSubMacSet = new HashSet<>();
        for (SubDeviceDO sub : subDeviceList) {
            SubDeviceRuntimeVO subDeviceRuntimeVO = map.get(sub.getMac());
            if (!Objects.equals(sub.getName(), subDeviceRuntimeVO.getName())) {
                sub.setName(subDeviceRuntimeVO.getName());
                subDeviceRepository.save(sub);
            }
            existSubMacSet.add(sub.getMac());
        }
        List<SubDeviceDO> saveList = map.values().stream().filter(v -> !existSubMacSet.contains(v.getMac())).map(subDeviceRuntimeVO -> {
            SubDeviceDO subDeviceDO = new SubDeviceDO();
            subDeviceDO.setId(YitIdHelper.nextId());
            subDeviceDO.setDeviceId(deviceId);
            subDeviceDO.setMac(subDeviceRuntimeVO.getMac());
            subDeviceDO.setName(subDeviceRuntimeVO.getName());
            return subDeviceDO;
        }).collect(Collectors.toList());
        subDeviceRepository.saveAll(saveList);
        subDeviceList.addAll(saveList);
        int timePlus = 0;
        for (SubDeviceDO subDeviceDO : subDeviceList) {
            SubDeviceRuntimeVO vo = map.get(subDeviceDO.getMac());
            DeviceRunTimeDO deviceRunTimeDO = new DeviceRunTimeDO();
            deviceRunTimeDO.setRunTime(vo.getRunTime());
            if (vo.getRunTime() > DateUtils.ONE_DAY_SECONDS) {
                deviceRunTimeDO.setRunTime(DateUtils.ONE_DAY_SECONDS);
            }
            deviceRunTimeDO.setSubId(subDeviceDO.getId());
            deviceRunTimeDO.setDate(yesterday);
            deviceRunTimeDO.setCreateTime(new Date(yesterdayTime + timePlus));
            deviceRunTimeDO.setDeviceId(deviceId);
            deviceRuntimeMapper.saveDeviceRunTime(deviceRunTimeDO);
            timePlus++;
        }
    }
}
