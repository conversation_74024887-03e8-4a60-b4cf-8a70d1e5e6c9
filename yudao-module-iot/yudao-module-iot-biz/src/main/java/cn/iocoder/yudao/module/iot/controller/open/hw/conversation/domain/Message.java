package cn.iocoder.yudao.module.iot.controller.open.hw.conversation.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Message {
    public static final int RATING_UP=1;
    public static final int RATING_DOWN=-1;
    public static final int RATING_UNDO=0;
    public static final int STATE_NORMAL=0;
    public static final int STATE_STOP=1;
    private String id;
    private String role; // system/user/assistant
    private String content;
    private Date timestamp;
    private Integer rating=0; // 1表示点赞，-1表示点踩，0表示未操作
    private Integer state=0;
}