package cn.iocoder.yudao.module.iot.controller.open.hw;

import cn.hutool.core.thread.ThreadUtil;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.iot.config.HuaweiProperties;
import cn.iocoder.yudao.module.iot.controller.open.hw.vo.CodeRedictReqVo;
import cn.iocoder.yudao.module.iot.controller.open.hw.vo.HuaweiTokenInfoRespVo;
import cn.iocoder.yudao.module.iot.controller.open.hw.vo.HuaweiTokenRespVo;
import cn.iocoder.yudao.module.iot.controller.open.hw.vo.IDTokenVo;
import cn.iocoder.yudao.module.iot.dal.dataobject.hw.HuaweiOAuthTokenDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.hw.HwUserHealthDO;
import cn.iocoder.yudao.module.iot.dal.mongo.HuaweiOAuthTokenRepository;
import cn.iocoder.yudao.module.iot.dal.mongo.HuaweiUserHealthRepository;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.Date;
import java.util.UUID;
import java.util.regex.Pattern;

@Api(tags = "用户 APP - 通知公告")
@RestController
@Slf4j
@RequestMapping("/open-api/hw/oauth")
public class HWController {

    public HWController() {
        this.restTemplate = new RestTemplate();
    }

    @Resource
    private HuaweiProperties huaweiProperties;
    private RestTemplate restTemplate;
    @Resource
    private HuaweiOAuthTokenRepository tokenRepository;
    @Resource
    private HwHearlthService hwHearlthService;

    /**
     * https://oauth-login.cloud.huawei.com/oauth2/v3/authorize?response_type=code&state=state_parameter_passthrough_value&client_id=113996631&redirect_uri=https://ivs.chinamobiledevice.com:11443/open-api/hw/oauth/code-redict&scope=openid+profile+https://www.huawei.com/healthkit/step.read+https://www.huawei.com/healthkit/calories.read+https://www.huawei.com/healthkit/distance.read+https://www.huawei.com/healthkit/strength.read+https://www.huawei.com/healthkit/activehours.read+https://www.huawei.com/healthkit/heightweight.read+https://www.huawei.com/healthkit/heightweight.read+https://www.huawei.com/healthkit/sleep.read+https://www.huawei.com/healthkit/sleep.read+https://www.huawei.com/healthkit/heartrate.read+https://www.huawei.com/healthkit/stress.read+https://www.huawei.com/healthkit/bloodglucose.read+https://www.huawei.com/healthkit/bloodpressure.read+https://www.huawei.com/healthkit/oxygensaturation.read+https://www.huawei.com/healthkit/bodytemperature.read+https://www.huawei.com/healthkit/hearthealth.read+https://www.huawei.com/healthkit/emotion.read+https://www.huawei.com/healthkit/dailyactivitysummary.read+https://www.huawei.com/healthkit/activityrecord.read+https://www.huawei.com/healthkit/activity.read&access_type=offline&display=touch
     *
     * @param codeRedictReqVo
     * @return
     */
    @Resource
    private HuaweiUserHealthRepository userHealthRepository;

    @GetMapping("/get-info")
    public CommonResult getInfo(@RequestHeader(required = false, value = "X-Open-Id") String openId){
        log.info("调用get-info接口{}",openId);
        if(StringUtils.isBlank(openId)){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        CommonResult result=hwHearlthService.getUserInfo(openId);
        log.info("get-info 响应 {}",JSON.toJSONString(result));
        if (result.isError()) {
            // 使用 ResponseEntity 返回重定向响应
            // 这里替换成你需要重定向的实际URL
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }else{
            return result;
        }

    }
    @PostMapping("/stop-service")
    public CommonResult logout(@RequestHeader(required = false, value = "X-Open-Id") String openId,
                               @RequestHeader(required = false, value = "X-Open-Id") String token){
        log.info("收到停止服务请求 {}",openId);
        return hwHearlthService.cancelAuthorizationByOpenId(openId);
    }
    private static final Pattern URL_ENCODED_PATTERN =
            Pattern.compile("%[0-9A-Fa-f]{2}");
    public String smartUrlDecode(String code) {
        if (code == null || code.isEmpty()) {
            return code;
        }
        try {
            // 检测是否包含URL编码
            if (URL_ENCODED_PATTERN.matcher(code).find()) {
                String decoded = URLDecoder.decode(code);
               log.info("检测到URL编码，解码: " + code + " -> " + decoded);
                return decoded;
            }

            // 检测是否包含空格需要处理
            if (code.contains(" ")) {
                String cleaned = code.replace(" ", "+");
                System.out.println("处理空格字符: " + code + " -> " + cleaned);
                return cleaned;
            }

            log.info("无需解码: " + code);
            return code;

        } catch (Exception e) {
            log.info("解码失败: " + e.getMessage());
            return code;
        }
    }
    @GetMapping("/code-redict")
    public ResponseEntity<?> codeRedict(CodeRedictReqVo codeRedictReqVo) {
        log.info("收到华为oauthCode: {}", JSON.toJSONString(codeRedictReqVo));

        // 1. 错误处理
        if (codeRedictReqVo.getError() != null) {
            HttpHeaders headers = new HttpHeaders();
            headers.setLocation(URI.create("https://ivs.chinamobiledevice.com:11443/hw/about"));
            return new ResponseEntity<>(headers, HttpStatus.MOVED_PERMANENTLY);
        }

        // 2. 授权码换token

        String code = smartUrlDecode(codeRedictReqVo.getCode());
        if (code == null || code.isEmpty()) {
            return ResponseEntity.badRequest()
                    .body(CommonResult.error(400, "缺少授权码code"));
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("grant_type", "authorization_code");
            params.add("code", code);
            params.add("client_id", huaweiProperties.getClientId());
            params.add("client_secret", huaweiProperties.getClientSecret());
            params.add("redirect_uri", huaweiProperties.getRedirectUri());
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(huaweiProperties.getTokenUrl(), request, String.class);
            String body = response.getBody();
            log.info("华为token响应: {}", body);
            HuaweiTokenRespVo tokenResponse = JSON.parseObject(response.getBody(), HuaweiTokenRespVo.class);
            if (tokenResponse == null || tokenResponse.getAccess_token() == null) {
                return ResponseEntity.badRequest()
                        .body(CommonResult.error(500, "获取token失败"));
            }

//            ThreadUtil.sleep(5000);
            IDTokenVo idTokenVo= new IDTokenParser().verify(tokenResponse.getId_token());
            HuaweiTokenInfoRespVo clientInfo = hwHearlthService.getClientTokenInfo(tokenResponse.getAccess_token());
            String openId = clientInfo.getOpen_id();

            HuaweiOAuthTokenDO tokenDO = tokenRepository.findFirstByOpenidOrderByCreatedAtDesc(openId);
            if (tokenDO == null) {
                tokenDO = new HuaweiOAuthTokenDO();
                tokenDO.setId(UUID.randomUUID().toString());
            }
            if(idTokenVo!=null){
                tokenDO.setDisplay_name(idTokenVo.getDisplay_name());
                tokenDO.setNickname(idTokenVo.getNickname());
                tokenDO.setPicture(idTokenVo.getPicture());
            }
            tokenDO.setOpenid(openId);

            tokenDO.setAccessToken(tokenResponse.getAccess_token());
            tokenDO.setExpiresIn(tokenResponse.getExpires_in());
            tokenDO.setIdToken(tokenResponse.getId_token());
            tokenDO.setRefreshToken(tokenResponse.getRefresh_token());
            tokenDO.setScopes(Arrays.asList(StringUtils.split(tokenResponse.getScope(), " ")));
            tokenDO.setTokenType(tokenResponse.getToken_type());
            tokenDO.setCreatedAt(System.currentTimeMillis());
            tokenDO.setAccessTokenUpdateAt(System.currentTimeMillis());
            tokenDO.setUnionid(clientInfo.getUnion_id());
//            BeanUtil.copyProperties(idTokenVo,tokenDO);
            tokenRepository.save(tokenDO);
            log.info("token is {}", JSON.toJSONString(tokenDO));
            HuaweiOAuthTokenDO finalTokenDO = tokenDO;
            ThreadUtil.execute(() -> {
                CommonResult<HwUserHealthDO> sampleReult = hwHearlthService.getSamplePoints(finalTokenDO.getAccessToken(), finalTokenDO.getScopes());
                log.info("sampleReult is {}", JSON.toJSONString(sampleReult));
                if (sampleReult.isSuccess()) {
                    HwUserHealthDO hwUserHealthDO = sampleReult.getData();
                    hwUserHealthDO.setId(finalTokenDO.getOpenid());
                    hwUserHealthDO.setCreated_at(new Date());
                    userHealthRepository.save(hwUserHealthDO);
                }

                hwHearlthService.removeAllHwCahceByOpenId(openId);
            });
            // 解析token响应（可根据实际返回结构调整VO）
            HttpHeaders respHeaders = new HttpHeaders();
            if( openId.equals(huaweiProperties.getDevOpenId())){
                log.info("openId {} 跳转本地{}",openId,huaweiProperties.getDevFrontendUrl());
                headers.setLocation(URI.create(huaweiProperties.getDevFrontendUrl()+"?openid="+finalTokenDO.getOpenid()+"&token="+finalTokenDO.getRefreshToken()));
                return new ResponseEntity<>(headers, HttpStatus.MOVED_PERMANENTLY);
            }

            if(CodeRedictReqVo.ENV_TEST.equals(codeRedictReqVo.getEnv())){
                headers.setLocation(URI.create(huaweiProperties.getTestFrontendUrl()+"?openid="+finalTokenDO.getOpenid()+"&token="+finalTokenDO.getRefreshToken()));
            }else if (CodeRedictReqVo.ENV_DEV.equals(codeRedictReqVo.getEnv())) {
                headers.setLocation(URI.create(huaweiProperties.getDevFrontendUrl()+"?openid="+finalTokenDO.getOpenid()+"&token="+finalTokenDO.getRefreshToken()));
            }else{
                headers.setLocation(URI.create("https://ivs.chinamobiledevice.com:11443/hw/?openid="+finalTokenDO.getOpenid()+"&token="+finalTokenDO.getRefreshToken()));
            }

            return new ResponseEntity<>(headers, HttpStatus.MOVED_PERMANENTLY);
        } catch (Exception e) {
            log.error("华为OAuth token获取异常", e);
            return ResponseEntity.badRequest()
                    .body(CommonResult.error(500, "华为OAuth token获取异常: " + e.getMessage()));
        }
    }


    public static void main(String[] args) {


    }
}
