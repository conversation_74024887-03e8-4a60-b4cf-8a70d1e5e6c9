package cn.iocoder.yudao.module.iot.util;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AudioUtil {
    public static byte[] addWavHeader(int SAMPLE_RATE,byte[] rawData) {
        // 检查是否已经包含WAV header
        if (rawData.length > 12 &&
                rawData[0] == 'R' && rawData[1] == 'I' && rawData[2] == 'F' && rawData[3] == 'F' &&
                rawData[8] == 'W' && rawData[9] == 'A' && rawData[10] == 'V' && rawData[11] == 'E') {
            log.info("Audio data already contains WAV header");
            return rawData; // 已经包含WAV header，直接返回
        }
        final int BITS_PER_SAMPLE = 16; // PCM16
        final int CHANNELS = 1; // 单声道
        final int HEADER_SIZE = 44;
        // 计算文件总长度
        long totalDataLen = (long)rawData.length + 36; // 36 + data size
        if (totalDataLen > Integer.MAX_VALUE) {
            throw new IllegalArgumentException("Audio data too large");
        }
        // 计算字节率 = 采样率 * 通道数 * (采样位数/8)
        int byteRate = SAMPLE_RATE * CHANNELS * (BITS_PER_SAMPLE / 8);
        byte[] header = new byte[HEADER_SIZE];
        // RIFF header
        header[0] = 'R'; header[1] = 'I'; header[2] = 'F'; header[3] = 'F';
        header[4] = (byte) (totalDataLen & 0xff);
        header[5] = (byte) ((totalDataLen >> 8) & 0xff);
        header[6] = (byte) ((totalDataLen >> 16) & 0xff);
        header[7] = (byte) ((totalDataLen >> 24) & 0xff);
        // WAVE header
        header[8] = 'W'; header[9] = 'A'; header[10] = 'V'; header[11] = 'E';
        // fmt chunk
        header[12] = 'f'; header[13] = 'm'; header[14] = 't'; header[15] = ' ';
        header[16] = 16; // subchunk size (16 for PCM)
        header[17] = 0;
        header[18] = 0;
        header[19] = 0;
        header[20] = 1; // PCM format = 1
        header[21] = 0;
        header[22] = (byte) CHANNELS; // channels
        header[23] = 0;
        // Sample rate
        header[24] = (byte) (SAMPLE_RATE & 0xff);
        header[25] = (byte) ((SAMPLE_RATE >> 8) & 0xff);
        header[26] = (byte) ((SAMPLE_RATE >> 16) & 0xff);
        header[27] = (byte) ((SAMPLE_RATE >> 24) & 0xff);
        // Byte rate
        header[28] = (byte) (byteRate & 0xff);
        header[29] = (byte) ((byteRate >> 8) & 0xff);
        header[30] = (byte) ((byteRate >> 16) & 0xff);
        header[31] = (byte) ((byteRate >> 24) & 0xff);
        // Block align
        header[32] = (byte) (CHANNELS * BITS_PER_SAMPLE / 8);
        header[33] = 0;
        // Bits per sample
        header[34] = (byte) BITS_PER_SAMPLE;
        header[35] = 0;
        // Data chunk header
        header[36] = 'd'; header[37] = 'a'; header[38] = 't'; header[39] = 'a';
        header[40] = (byte) (rawData.length & 0xff);
        header[41] = (byte) ((rawData.length >> 8) & 0xff);
        header[42] = (byte) ((rawData.length >> 16) & 0xff);
        header[43] = (byte) ((rawData.length >> 24) & 0xff);
        // Combine header and data
        byte[] wavData = new byte[header.length + rawData.length];
        System.arraycopy(header, 0, wavData, 0, header.length);
        System.arraycopy(rawData, 0, wavData, header.length, rawData.length);

        return wavData;
    }
}
