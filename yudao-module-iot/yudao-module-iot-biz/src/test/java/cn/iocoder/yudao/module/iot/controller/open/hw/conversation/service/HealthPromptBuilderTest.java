package cn.iocoder.yudao.module.iot.controller.open.hw.conversation.service;

import cn.iocoder.yudao.module.iot.controller.open.hw.vo.SleepVo;
import cn.iocoder.yudao.module.iot.controller.open.hw.vo.SportRecordVo;
import cn.iocoder.yudao.module.iot.dal.dataobject.hw.HwUserHealthDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * HealthPromptBuilder 测试类
 */
@SpringBootTest
public class HealthPromptBuilderTest {

    private HealthPromptBuilder healthPromptBuilder;

    @BeforeEach
    void setUp() {
        healthPromptBuilder = new HealthPromptBuilder();
    }

    @Test
    void testBuildChinesePrompt() {
        // 准备测试数据
        HwUserHealthDO hwUserHealthDO = new HwUserHealthDO();
        hwUserHealthDO.setScopes("步数,睡眠");
        
        List<Map> jsonObjects = new ArrayList<>();
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("date", "2024-01-01");
        healthData.put("steps", 8000);
        jsonObjects.add(healthData);
        
        List<SleepVo> sleepVos = new ArrayList<>();
        List<SportRecordVo> sportRecordVos = new ArrayList<>();
        
        String query = "我今天的步数如何？";
        
        // 测试中文提示词构建
        String chinesePrompt = healthPromptBuilder.buildSystemPrompt(
                "zh", hwUserHealthDO, jsonObjects, sleepVos, sportRecordVos, query);
        
        // 验证中文提示词包含关键内容
        assertNotNull(chinesePrompt);
        assertTrue(chinesePrompt.contains("灵犀健康智能助理"));
        assertTrue(chinesePrompt.contains("安全规则"));
        assertTrue(chinesePrompt.contains("回答规则"));
        assertTrue(chinesePrompt.contains("核心能力"));
        assertTrue(chinesePrompt.contains("使用中文回复"));
        
        System.out.println("=== 中文提示词 ===");
        System.out.println(chinesePrompt);
    }

    @Test
    void testBuildEnglishPrompt() {
        // 准备测试数据
        HwUserHealthDO hwUserHealthDO = new HwUserHealthDO();
        hwUserHealthDO.setScopes("steps,sleep");
        
        List<Map> jsonObjects = new ArrayList<>();
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("date", "2024-01-01");
        healthData.put("steps", 8000);
        jsonObjects.add(healthData);
        
        List<SleepVo> sleepVos = new ArrayList<>();
        List<SportRecordVo> sportRecordVos = new ArrayList<>();
        
        String query = "How are my steps today?";
        
        // 测试英文提示词构建
        String englishPrompt = healthPromptBuilder.buildSystemPrompt(
                "en", hwUserHealthDO, jsonObjects, sleepVos, sportRecordVos, query);
        
        // 验证英文提示词包含关键内容
        assertNotNull(englishPrompt);
        assertTrue(englishPrompt.contains("Lingxi Health AI Assistant"));
        assertTrue(englishPrompt.contains("Security Rules"));
        assertTrue(englishPrompt.contains("Answer Rules"));
        assertTrue(englishPrompt.contains("Core Capabilities"));
        assertTrue(englishPrompt.contains("Reply in English"));
        
        System.out.println("=== 英文提示词 ===");
        System.out.println(englishPrompt);
    }

    @Test
    void testBuildPromptWithoutUserData() {
        // 测试没有用户数据的情况
        String prompt = healthPromptBuilder.buildSystemPrompt(
                "zh", null, null, null, null, "你好");
        
        assertNotNull(prompt);
        assertTrue(prompt.contains("用户未授权个人的数据"));
        
        System.out.println("=== 无用户数据提示词 ===");
        System.out.println(prompt);
    }

    @Test
    void testLanguageConsistency() {
        // 测试语言一致性
        String chinesePrompt = healthPromptBuilder.buildSystemPrompt(
                "zh", null, null, null, null, "测试");
        String englishPrompt = healthPromptBuilder.buildSystemPrompt(
                "en", null, null, null, null, "test");
        
        // 中文提示词应该包含中文内容
        assertTrue(chinesePrompt.contains("灵犀健康智能助理"));
        assertTrue(chinesePrompt.contains("使用中文回复"));
        
        // 英文提示词应该包含英文内容
        assertTrue(englishPrompt.contains("Lingxi Health AI Assistant"));
        assertTrue(englishPrompt.contains("Reply in English"));
        
        // 两个提示词应该不同
        assertNotEquals(chinesePrompt, englishPrompt);
    }

    @Test
    void testDefaultLanguage() {
        // 测试默认语言（应该是中文）
        String prompt = healthPromptBuilder.buildSystemPrompt(
                "unknown", null, null, null, null, "测试");
        
        // 未知语言应该默认使用中文
        assertTrue(prompt.contains("灵犀健康智能助理"));
    }
}
