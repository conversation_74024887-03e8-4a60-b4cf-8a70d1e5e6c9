package cn.iocoder.yudao.module.iot.xiaodu;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.IpUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.infra.api.file.dto.FileRespDTO;
import cn.iocoder.yudao.module.iot.config.BaiduAsrProperties;
import cn.iocoder.yudao.module.iot.config.TaskExecutorConfig;
import cn.iocoder.yudao.module.iot.controller.device.vo.AiCommonHeader;
import cn.iocoder.yudao.module.iot.dal.dataobject.ai.LogHistory;
import cn.iocoder.yudao.module.iot.dal.mongo.LogHistoryRepository;
import cn.iocoder.yudao.module.iot.enums.ai.AiApiNameConstants;
import cn.iocoder.yudao.module.iot.util.ProductUtils;
import cn.iocoder.yudao.module.iot.xiaodu.dto.NoteSummaryReqVo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.reactivestreams.Publisher;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;

@Slf4j
@Component
public class BaiduInsideRcClient {

    @Setter
    @Resource
    private WebClient webClient;
    @Resource
    @Setter
    private BaiduAsrProperties baiduAsrProperties;
    @Resource
    private LogHistoryRepository logHistoryRepository;
    private com.alibaba.fastjson2.JSONObject buildAIDeviceInfoContext(AiCommonHeader aiCommonHeader){

        com.alibaba.fastjson2.JSONObject deviceInfoContext=new com.alibaba.fastjson2.JSONObject();
        com.alibaba.fastjson2.JSONObject namespace= new com.alibaba.fastjson2.JSONObject();
        namespace.put("namespace","ai.fxzsos.device_interface.ai_device_info");
        namespace.put("name","AiDeviceInfo");
        com.alibaba.fastjson2.JSONObject payload= new com.alibaba.fastjson2.JSONObject();
        payload.put("x_ai_vid",aiCommonHeader.getProductId());
        String deviceId = aiCommonHeader.getDeviceId();
        if(deviceId==null){
            deviceId =aiCommonHeader.getDeviceNo();
        }
        payload.put("x_ai_uid",deviceId);
        payload.put("x_ai_ip", IpUtils.getIPAddress(ServletUtils.getRequest()));
        payload.put("model","jiutian-lan");
        deviceInfoContext.put("header",namespace);
        deviceInfoContext.put("payload",payload);
        return deviceInfoContext;
    }

    public Publisher<JSONObject> noteSummary(AiCommonHeader aiCommonHeader, NoteSummaryReqVo reqVo) {
        String appid = baiduAsrProperties.getAppid();
        String appkey = baiduAsrProperties.getAppkey();
        long salt = System.currentTimeMillis();
        String sign = DigestUtils.md5Hex(appid + appkey + salt) + ":" + salt;
        long ts = System.currentTimeMillis();

        LogHistory logHistory = new LogHistory();
        logHistory.setTraceId(String.valueOf(aiCommonHeader.getTs()));
        logHistory.setDeviceId(aiCommonHeader.getDeviceId());
        logHistory.setProductId(aiCommonHeader.getProductId());
        logHistory.setReqFile(null);
        logHistory.setDeviceNo(aiCommonHeader.getDeviceNo());
        String query = JSON.toJSONString(reqVo);
        logHistory.setReqMsgs(Lists.newArrayList(new LogHistory.Message(ts, query)));
        logHistory.setRespMsgs(new ArrayList<>(32));
        logHistory.setId(IdUtil.objectId());
        logHistory.setApiName(AiApiNameConstants.NOTE_SUMMARY);

        // 根据 stream 字段判断是否使用流式响应
        boolean isStream = reqVo.getStream() != null && reqVo.getStream();

        if (isStream) {
            // 流式响应处理
            return webClient.post()
                    .uri(baiduAsrProperties.getNoteSummaryurl())
                    .header("Client-Id", appid)
                    .header("Authorization", sign)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(query)
                    .retrieve()
                    .bodyToFlux(String.class)
                    .map(chunk -> {
                        // 处理每个数据块
                        JSONObject obj = JSONObject.parseObject(chunk);
                        logHistory.getRespMsgs().add(new LogHistory.Message(System.currentTimeMillis(), chunk));
                        return obj;
                    })
                    .doOnError(throwable -> {
                        log.error("Stream error", throwable);
                    })
                    .doFinally(signalType -> {
                        logHistory.setCreateTime(new Date());
                        logHistory.setTotalCost(System.currentTimeMillis() - ts);
                        TaskExecutorConfig.getInstance().execute(() -> {
                            logHistoryRepository.save(logHistory);
                        });
                    });
        } else {
            // 非流式响应处理
            return webClient.post()
                    .uri(baiduAsrProperties.getNoteSummaryurl())
                    .header("Client-Id", appid)
                    .header("Authorization", sign)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(query)
                    .retrieve()
                    .bodyToMono(String.class)
                    .flatMap(s -> {
                        JSONObject obj = JSONObject.parseObject(s);
                        logHistory.getRespMsgs().add(new LogHistory.Message(System.currentTimeMillis(), s));
                        return Mono.just(obj);
                    })
                    .doOnError(throwable -> {
                        log.error("Error", throwable);
                    })
                    .doFinally(signalType -> {
                        logHistory.setCreateTime(new Date());
                        logHistory.setTotalCost(System.currentTimeMillis() - ts);
                        TaskExecutorConfig.getInstance().execute(() -> {
                            logHistoryRepository.save(logHistory);
                        });
                    });
        }
    }

    public Publisher<?> chat(AiCommonHeader aiCommonHeader,String query) {
        JSONObject queryObj = JSONObject.parseObject(query);
        // 生成签名
        long salt = System.currentTimeMillis();
        String appid=baiduAsrProperties.getAppid();
        String appkey =baiduAsrProperties.getAppkey();
        if(baiduAsrProperties.getLaikuProductId().equals(aiCommonHeader.getProductId())){
            appid =baiduAsrProperties.getLaikuAppId();
            appkey=baiduAsrProperties.getLaikuAppKey();
            queryObj.put("version",baiduAsrProperties.getLaikuVersion());
        }
        String sign = DigestUtils.md5Hex(appid + appkey + salt) + ":" + salt;
        long ts=System.currentTimeMillis();
        LogHistory logHistory=new LogHistory();;
        if(aiCommonHeader!=null){
            logHistory.setTraceId(String.valueOf(aiCommonHeader.getTs()));
            logHistory.setDeviceId(aiCommonHeader.getDeviceId());
            logHistory.setProductId(aiCommonHeader.getProductId());
            logHistory.setReqFile(null);
            logHistory.setDeviceNo(aiCommonHeader.getDeviceNo());
            logHistory.setReqMsgs(Lists.newArrayList(new LogHistory.Message(ts, query)));
            logHistory.setRespMsgs(Collections.emptyList());
            logHistory.setRespMsgs(new ArrayList<>(32));
            logHistory.setId(IdUtil.objectId());
            logHistory.setApiName(AiApiNameConstants.INSIDE_RC_CHAT);
        }
        query=query.replace("fxzsos","dueros");


        JSONArray clientContextArr= queryObj.getJSONArray("client_context");
        if(clientContextArr==null){
            clientContextArr=new JSONArray();
            queryObj.put("client_context",clientContextArr);
        }
        queryObj.put("client_ip",IpUtils.getIPAddress(ServletUtils.getRequest()));
        clientContextArr.add(buildAIDeviceInfoContext(aiCommonHeader));
        return webClient.post()
                .uri(baiduAsrProperties.getInsideRcUrl())
                .header("Client-Id", appid)
                .header("Authorization", sign)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(queryObj.toString())
                .retrieve()
                .bodyToFlux(String.class)
                .map(chunk -> {
                    chunk = chunk.replace("dueros","fxzsos")
                            .replace("duer:","fxzsos:");
                    // 处理返回的数组格式
                    if(logHistory.getId()!=null){
                        logHistory.getRespMsgs().add(new LogHistory.Message(System.currentTimeMillis(),chunk));
                    }
                    JSONObject obj=JSON.parseObject(chunk);
//                    if(Objects.equals(obj.getInteger("is_end"),1)){
//                        if(logHistory.getId()!=null) {
//                            logHistory.setCreateTime(new Date());
//                            logHistory.setTotalCost(System.currentTimeMillis() - ts);
//                            TaskExecutorConfig.getInstance().execute(() -> {
//                                logHistoryRepository.save(logHistory);
//                            });
//                        }
//                    }
                    return ServerSentEvent.<String>builder()
                            .data(chunk)
                            .build();
                }).doFinally(signalType -> {
                    if(logHistory.getId()!=null) {
                        logHistory.setCreateTime(new Date());
                        logHistory.setTotalCost(System.currentTimeMillis() - ts);
                        TaskExecutorConfig.getInstance().execute(() -> {
                            logHistoryRepository.save(logHistory);
                        });
                    }
                })
                .doOnError(error -> log.error("Stream error occurred: ", error));
//        }
    }

    @Resource
    private BaiduDrawImageClient drawImageClient;
    @Resource
    private FileApi fileApi;
    public Mono<CommonResult<BaiduDrawImageClient.AigcResponse>> draw(AiCommonHeader aiCommonHeader,BaiduDrawImageClient.GenerateParams params) {
        Date start =new Date();
        long ts =start.getTime();
        LogHistory logHistory=new LogHistory();
        if(aiCommonHeader!=null){
            logHistory.setTraceId(String.valueOf(aiCommonHeader.getTs()));
            logHistory.setDeviceId(aiCommonHeader.getDeviceId());
            logHistory.setProductId(aiCommonHeader.getProductId());
            logHistory.setReqFile(null);
            logHistory.setDeviceNo(aiCommonHeader.getDeviceNo());
            logHistory.setReqMsgs(Lists.newArrayList(new LogHistory.Message(ts, JSON.toJSONString(params))));
            logHistory.setRespMsgs(Collections.emptyList());
            logHistory.setRespMsgs(new ArrayList<>(32));
            logHistory.setApiName(AiApiNameConstants.SYNC_DRAW_IMAGE);
            logHistory.setId(IdUtil.objectId());
            if(params.getUserId()==null){
                params.setUserId(aiCommonHeader.getDeviceId());
            }
        }
        try {
            BaiduDrawImageClient.AigcResponse response = drawImageClient.generateImage(params);
            CommonResult<BaiduDrawImageClient.AigcResponse>result=    CommonResult.result(response.getErrCode(),response.getErrMsg(),response);
            long end = System.currentTimeMillis();
            logHistory.setRespMsgs(Lists.newArrayList(new LogHistory.Message(end,JSON.toJSONString(result))));
            if(logHistory.getId()!=null){
                TaskExecutorConfig.getInstance().execute(() -> {
                    long cost = end-ts;
                    logHistory.setTotalCost(cost);
                    if(result.isSuccess()){
                        BaiduDrawImageClient.AigcResponse resp=result.getData();
                        if(!resp.getImages().isEmpty()){
                            String image=resp.getImages().get(0);
                            byte[] imageByte=  HttpUtil.downloadBytes(image);
                            String path = ProductUtils.getLogFilePath(aiCommonHeader.getProductId(),aiCommonHeader.getDeviceId());
                            String fileName=start+"_imageDraw.png";
                            FileRespDTO fileRespDTO=fileApi.createFileWithDetail(path,fileName,fileName,imageByte);
                            logHistory.setRespFile(new LogHistory.FileRecord(ts,fileRespDTO.getSize(),fileRespDTO.getType(),fileRespDTO.getUrl()));
                        }

                    }
                    logHistory.setCreateTime(new Date());
                    logHistoryRepository.save(logHistory);
                });

            }
            return Mono.just(CommonResult.result(response.getErrCode(),response.getErrMsg(),response));
        } catch (Exception e) {
            CommonResult result = CommonResult.error(500, "任务执行失败");
            if (logHistory.getId() != null) {
                TaskExecutorConfig.getInstance().execute(() -> {
                    long end = System.currentTimeMillis();
                    long cost = end - ts;
                    logHistory.setTotalCost(cost);
                    logHistory.setCreateTime(new Date());
                    logHistoryRepository.save(logHistory);
                });
            }
            return Mono.just(CommonResult.error(500, "任务执行失败"));
        }
    }

    public static void main(String[] args) {
//        BaiduInsideRcClient insideRcClient=new BaiduInsideRcClient();
//        insideRcClient.setBaiduAsrProperties(new BaiduAsrProperties());
//        insideRcClient.setWebClient(WebClient.builder().build());
//        NoteSummaryReqVo req=new NoteSummaryReqVo();
        Long salt=System.currentTimeMillis();
//        req.setContent("会议摘要：\\n本次会议主要围绕用户日活减少8%的问题展开讨论。初步排查技术故障后，发现新用户留存率上涨3%，推测与运营上周五推出的弹窗活动有关。然而，用户反馈中提及页面卡顿，且存在安卓低端机型兼容性问题。会议决定，技术团队需立即提供性能分析报告，运营团队需拉取活动用户分群数据，设计团队重新走查弹窗交互流程，并特别关注推荐算法更新时间点与用户日活下降是否有关联。此外，市场部的投放用户画像问题也需进一步调查。会议要求，所有人需按照优先级处理当前问题，特别是要尽快找到日活下降的根本原因。\\n\\n待办事项：\\n\\n1. 技术团队：半小时内提供性能分析报告。\\n2. 运营团队：拉取活动用户分群数据，分析用户行为路径。\\n3. 设计团队：重新走查弹窗交互流程，确保用户体验。\\n4. 算法团队：验证推荐算法更新时间点与用户日活下降是否有关。\\n5. 客服团队：关注支付失败投诉，分析原因并记录风险清单。\\n6. 市场部：调查投放用户画像问题，确认是否存在刷量行为。");
        String sign = DigestUtils.md5Hex("gNCcuuUkSB63XXEK8Q1ljjPSBrxAqyPY" + "GDZlYoLrLZ1jQCBq8W7jKS6aD0rxdB74" + salt) + ":" + salt;
        System.out.println(sign);
 //       Mono<JSONObject>resultMono=  insideRcClient.noteSummary(null,req);
    //    System.out.println(resultMono.block());

    }
}
