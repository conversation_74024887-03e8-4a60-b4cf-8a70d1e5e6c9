package cn.iocoder.yudao.module.iot.controller.open.hw.conversation.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.module.iot.controller.open.hw.conversation.domain.ChatMessage;
import cn.iocoder.yudao.module.iot.controller.open.hw.conversation.domain.Message;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.Disposable;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LLMServiceImpl implements LLMService {
    

    private String apiUrl="https://ark.cn-beijing.volces.com/api/v3/chat/completions";
    

    private String apiKey="673d2aa3-5554-46dd-ab47-a0d5a48d6782";
    
    @Autowired
    private WebClient webClient;
    
    @Override
    public String getResponse(List<Message> messages) {
        // 实现普通响应逻辑
        // ...
        return null;
    }
    
    @Override
    public void getStreamResponse(String systemPrompt,List<Message> messages,StreamStatus streamStatus,LLMStreamCallback callback) {
        try {
            // 构建请求体
            List<ChatMessage> allMsgs=new ArrayList<>(messages.size()+1);
            allMsgs.add(new ChatMessage("system",systemPrompt));
            // 获取最后5轮对话
            int startIndex = Math.max(0, messages.size() - 10); // 因为一轮对话包含用户和助手的消息，所以是10条消息
            List<Message> recentMessages = messages.subList(startIndex, messages.size());
            Message lastMessage = messages.get(messages.size()-1);
            if(attackDetection(lastMessage,callback)){
                return;
            }

            // 添加最近的消息
            recentMessages.forEach(message -> {
                allMsgs.add(new ChatMessage(message.getRole(), message.getContent()));
            });
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("messages", allMsgs);
            requestBody.put("stream", true);
            requestBody.put("model","doubao-1-5-pro-32k-250115");

            log.info("请求大模型{} {}", apiUrl,JSON.toJSONString(requestBody));
            webClient.post()
                .uri(apiUrl)
                .header("Authorization", "Bearer " + apiKey)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToFlux(String.class)
                    .takeWhile(chunk -> !streamStatus.isCancelled()) // 当状态为取消时停止流
                .subscribe(
                    chunk -> {
                        if (streamStatus.isCancelled()) {
                            return; // 直接返回，不处理
                        }
                        // 解析响应chunk，提取token
                        String token = parseToken(chunk);
                        callback.onToken(token);
                    },
                    callback::onError,
                    callback::onComplete
                );
                
        } catch (Exception e) {
            callback.onError(e);
        }
    }
    private boolean attackDetection(Message message,LLMStreamCallback llmStreamCallback){
        String content =message.getContent();
        PromptInjectionDetector.DetectionResult result=PromptInjectionDetector.detectAttack(content);
        log.info("攻击检测分数{}",JSON.toJSONString(result));
        if(result.isAttack()){
            llmStreamCallback.onToken("抱歉，我无法回答你的问题。");
            llmStreamCallback.onComplete();
            return true;
        }
        return false;
    }

    @Override
    public List<String> getSuggestions( List<Message> messages) {
        StringBuilder sb=new StringBuilder("请帮助我预测人类最可能提出的三个问题，保持每个问题不超过20个字符。每个推荐问题输出一行。只回复推荐问题,问题不要带有序号，不要有其他回复。 以下是问答内容:\n");
        int startIndex = Math.max(0, messages.size() - 6); // 因为一轮对话包含用户和助手的消息，所以是6条消息
        List<Message> recentMessages = messages.subList(startIndex, messages.size());
        recentMessages.forEach(message -> {
            if(message.getRole().equals("user")){
                sb.append("User: ").append(message.getContent()).append("\n");
            }else if(message.getRole().equals("assistant")){
                sb.append("Assistant: ").append(message.getContent()).append("\n");
            }
        });

        List<ChatMessage> allMsgs=new ArrayList<>(1);
        allMsgs.add(new ChatMessage("system",sb.toString()));
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("messages", allMsgs);
        requestBody.put("stream", false);
        requestBody.put("model","doubao-1-5-pro-32k-250115");
        log.info("请求大模型{} {}", apiUrl,JSON.toJSONString(requestBody));
        String response= HttpRequest.post(apiUrl)
                .body(JSON.toJSONString(requestBody))
                .header("Authorization", "Bearer " + apiKey)
                .execute().body();

        if(JSON.isValid(response)){
            JSONObject obj=JSON.parseObject(response);
            String resp=obj.getJSONArray("choices").getJSONObject(0).getJSONObject("message")
                    .getString("content");
            List<String> suggestions = Arrays.stream(resp.split("\n"))
                    .map(String::trim)
                    .filter(StringUtils::isNoneBlank)
                    .map(s -> {
                        if(s.endsWith("吗")){
                            return s+"？";
                        }
                        return s;
                    })
                    .collect(Collectors.toList());
            return suggestions;
        }else{
            return Collections.emptyList();
        }
    }
    private static final String TITLE_PROMPT_TEMPLATE =
            "请为以下大模型对话生成一个简洁的标题。\n" +
                    "## 要求\n" +
                    "- 如果对话使用的是英文就用英文标题，如果是中文就生成中文标题\n" +
                    "- 标题要自然流畅，像是用户自己给对话起的名字\n" +
                    "- 保持简短（中文不超过15个字，英文不超过50个字符）\n" +
                    "- 直接输出标题内容，不要编号、引号或其他格式\n" +
                    "- 避免使用\"用户询问\"、\"助手回复\"等机械化表述\n" +
                    "- 抓住对话的核心话题或用户的主要意图\n" +
                    "- 使用口语化、易懂的表达\n" +
                    "## 示例\n" +
                    "- 如果对话是关于天气，标题可以是：明天的天气预报查询\n" +
                    "- 如果对话是关于今天步数，标题可以是：查询今天的步数\n" +
                    "- 如果对话是关于健康咨询，标题可以是：头痛该怎么缓解\n\n" +
                    "## 对话内容\n";
    @Override
    public String getSummary(List<Message> messages) {
        // 构造摘要提示语
        StringBuilder sb = new StringBuilder(TITLE_PROMPT_TEMPLATE);

        messages.forEach(message -> {
            if ("user".equals(message.getRole())) {
                sb.append("用户提问：").append(message.getContent()).append("\n");
            } else if ("assistant".equals(message.getRole())) {
                sb.append("助手回复：").append(message.getContent()).append("\n");
            }
        });


        List<ChatMessage> allMsgs = new ArrayList<>(1);
        allMsgs.add(new ChatMessage("system", sb.toString()));
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("messages", allMsgs);
        requestBody.put("stream", false);
        requestBody.put("model", "doubao-1-5-pro-32k-250115");
        log.info("请求大模型{} {}", apiUrl, JSON.toJSONString(requestBody));
        String response = HttpRequest.post(apiUrl)
                .body(JSON.toJSONString(requestBody))
                .header("Authorization", "Bearer " + apiKey)
                .execute().body();

        if (JSON.isValid(response)) {
            JSONObject obj = JSON.parseObject(response);
            String summary = obj.getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("message")
                    .getString("content");
            return summary.trim();
        } else {
            return "";
        }
    }

    private String parseToken(String chunk) {
        return chunk;
    }
}
