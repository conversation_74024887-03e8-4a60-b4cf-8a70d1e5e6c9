package cn.iocoder.yudao.module.iot.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/16
 */
@Data
@Component
@ConfigurationProperties(prefix = "yunpan")
public class YunPanConfig {

    private List<ProviderConfig> config;

    public ProviderConfig getConfigByAppId(String appId) {
        if (appId == null || config == null) {
            return null;
        }

        return config.stream()
                .filter(providerConfig -> appId.equals(providerConfig.getAppId()))
                .findFirst()
                .orElse(null);
    }

    public ProviderConfig getConfigByProductId(String productId) {
        if (productId == null || config == null) {
            return null;
        }

        return config.stream()
                .filter(providerConfig -> productId.equals(providerConfig.getProductId()))
                .findFirst()
                .orElse(null);
    }

    @Data
    public static class ProviderConfig {
        private String name;
        private String appId;
        private String appKey;
        private String secretKey;
        private String productId;
        private List<String> allowUrl;
    }
}
