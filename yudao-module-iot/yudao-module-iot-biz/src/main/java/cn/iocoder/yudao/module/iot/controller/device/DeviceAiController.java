package cn.iocoder.yudao.module.iot.controller.device;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.encrypt.EncryptUtil;
import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.infra.api.file.dto.FileRespDTO;
import cn.iocoder.yudao.module.infra.api.file.dto.GenPresignedUrlDTO;
import cn.iocoder.yudao.module.iot.config.JiutianProperties;
import cn.iocoder.yudao.module.iot.config.TaskExecutorConfig;
import cn.iocoder.yudao.module.iot.controller.device.vo.*;
import cn.iocoder.yudao.module.iot.controller.open.ai.service.AiService;
import cn.iocoder.yudao.module.iot.controller.open.ai.vo.AudioToTextReqVO;
import cn.iocoder.yudao.module.iot.controller.open.ai.vo.ChatRequest;
import cn.iocoder.yudao.module.iot.dal.dataobject.ai.LogHistory;
import cn.iocoder.yudao.module.iot.dal.dataobject.device.DeviceDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.devicelogfile.DeviceLogFileDO;
import cn.iocoder.yudao.module.iot.dal.mongo.LogHistoryRepository;
import cn.iocoder.yudao.module.iot.enums.ai.AiApiNameConstants;
import cn.iocoder.yudao.module.iot.service.ai.AppAiService;
import cn.iocoder.yudao.module.iot.service.dify.DifyService;
import cn.iocoder.yudao.module.iot.util.AudioUtil;
import cn.iocoder.yudao.module.iot.util.CosUtil;
import cn.iocoder.yudao.module.iot.util.ProductUtils;
import cn.iocoder.yudao.module.iot.util.SignUtil;
import cn.iocoder.yudao.module.iot.xiaodu.*;
import cn.iocoder.yudao.module.iot.xiaodu.dto.*;
import cn.iocoder.yudao.module.system.config.AiProperties;
import cn.iocoder.yudao.module.system.service.dify.vo.DifyAppDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.FORBIDDEN;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.infra.enums.ErrorCodeConstants.FILE_FORMAT_IS_WRONG;
import static cn.iocoder.yudao.module.iot.enums.ErrorCodeConstants.DEVICE_NOT_EXISTS;

@Api(tags = "设备 - 设备上传文件相关")
@RestController
@RequestMapping("/device-api/ai")
@Validated
@Slf4j
public class DeviceAiController {
    @Resource
    private AppAiService appAiService;
    @Resource
    private AiService aiService;
    @Resource
    private AiProperties aiProperties;

    @Resource
    private SignUtil signUtil;

    @GetMapping("/v1/get-apps")
    public CommonResult<List<DifyAppDTO>> getAiAppList() {
        return CommonResult.success(appAiService.getAiAppList());
    }

    public AiCommonHeader buildAiCommonHeader(String deviceNo,String productId, String productKey, String deviceId, String sign, Long ts){
        return new AiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
    }

    @Resource
    private JiutianProperties jiutianProperties;
    @PostMapping(value = "/v1/chat/completions", produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE})
    public Publisher<?> chatCompletions(
            @RequestHeader(value = "deviceNo", required = true) String deviceNo,
            @RequestHeader(value = "productId", required = true) String productId,
            @RequestHeader(value = "productKey", required = true) String productKey,
            @RequestHeader(value = "deviceId", required = true) String deviceId,
            @RequestHeader(value = "sign", required = true) String sign,
            @RequestHeader(value = "ts", required = false) Long ts,
            @RequestBody ChatRequest request) {
        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        String token = jiutianProperties.getYd75bToken();

        if(!signUtil.checkSign(deviceId,ts,sign)){
            return Mono.just(CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED));
        }
        return aiService.chat(aiCommonHeader,request, token);
    }


    @Resource
    private BaiduTtsClient baiduTtsClient;
    @Resource
    private LogHistoryRepository logHistoryRepository;
    //    @PreAuthenticated
    @PostMapping(value = "/v2/text-to-audio", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity<?> textToAudio2(
            @RequestHeader(value = "deviceNo", required = true) String deviceNo,
            @RequestHeader(value = "productId", required = true) String productId,
            @RequestHeader(value = "productKey", required = true) String productKey,
            @RequestHeader(value = "deviceId", required = true) String deviceId,
            @RequestHeader(value = "sign", required = true) String sign,
            @RequestHeader(value = "ts", required = false) Long ts,
            @RequestBody TtsRequest ttsRequest) throws UnsupportedEncodingException {
        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        log.info("Request Body: {}", ttsRequest);
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
                    .body(JSON.toJSONString(CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED)));
        }
        long start =System.currentTimeMillis();
        String format = TtsConstants.Format.FORMATS.get(ttsRequest.getAue());
        String contentType = format.equals("mp3") ? "audio/mpeg" : "audio/" + format;
        byte[] speechFile  = baiduTtsClient.textToSpeech(ttsRequest);
        long end = System.currentTimeMillis();
        long cost = end-start;
        TaskExecutorConfig.getInstance().execute( ()->{
            LogHistory logHistory=new LogHistory();
            logHistory.setTraceId(String.valueOf(aiCommonHeader.getTs()));
            logHistory.setDeviceId(aiCommonHeader.getDeviceId());
            logHistory.setProductId(aiCommonHeader.getProductId());
            logHistory.setDeviceNo(aiCommonHeader.getDeviceNo());
            logHistory.setApiName(AiApiNameConstants.TEXT_TO_AUDIO);
            logHistory.setReqFile(null);
            logHistory.setReqMsgs(Lists.newArrayList(new LogHistory.Message(start,JSON.toJSONString(ttsRequest))));
            logHistory.setRespMsgs(Collections.emptyList());
            String path = ProductUtils.getLogFilePath(productId,deviceId);
            String fileName=start+"_tts.wav";
            FileRespDTO fileRespDTO= fileApi.createFileWithDetail(path,fileName,fileName,speechFile);
            if(fileRespDTO!=null){
                logHistory.setRespFile(new LogHistory.FileRecord(end,fileRespDTO.getSize(),fileRespDTO.getType(),fileRespDTO.getUrl()));
            }
            logHistory.setTotalCost(cost);
            logHistory.setCreateTime(new Date());
            logHistory.setId(IdUtil.objectId());
            logHistoryRepository.save(logHistory);
        });

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .body(speechFile);
    }


    @PreAuthenticated
    @PostMapping("/v1/audio-base64-to-text")
    public CommonResult<String> audio2Text(@RequestHeader(value = "deviceNo", required = true) String deviceNo,
                                           @RequestHeader(value = "productId", required = true) String productId,
                                           @RequestHeader(value = "productKey", required = true) String productKey,
                                           @RequestHeader(value = "deviceId", required = true) String deviceId,
                                           @RequestHeader(value = "sign", required = true) String sign,
                                           @RequestHeader(value = "ts", required = false) Long ts,
                                           @RequestBody AudioToTextReqVO reqVO) {
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        return aiService.audio2Text(aiCommonHeader,reqVO);
    }

    @Resource
    private BaiduASRClient asrClient;

    @PreAuthenticated
    @PostMapping(value = "/v1/audio-to-text", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult<String> audioFileToText(
            @RequestHeader(value = "deviceNo", required = true) String deviceNo,
            @RequestHeader(value = "productId", required = true) String productId,
            @RequestHeader(value = "productKey", required = true) String productKey,
            @RequestHeader(value = "deviceId", required = true) String deviceId,
            @RequestHeader(value = "sign", required = true) String sign,
            @RequestHeader(value = "ts", required = false) Long ts,
            @RequestPart("file") MultipartFile audioFile,
            @RequestParam(value = "format", defaultValue = "mp3") String format) {
        try {
            byte[] audioBytes = audioFile.getBytes();
            if(!signUtil.checkSign(deviceId,ts,sign)){
                return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
            }
            ASRResult result = asrClient.convertAudioToText(audioBytes).get();
            if (result.isSuccess()) {
                return CommonResult.success(result.getText());
            } else {
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("处理音频文件失败", e);
            return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), "处理音频文件失败");
        }
    }

    @Resource
    private BaiduASRChatClient asrChatClient;
    @Resource
    private BaiduAsrChatClientAsync asrChatClientAsync;

    @PreAuthenticated
    @PostMapping(value = "/v2/asr-chat", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public SseEmitter asrChat(
            @RequestHeader(value = "deviceNo", required = false) String deviceNo,
            @RequestHeader(value = "productId", required = false) String productId,
            @RequestHeader(value = "productKey", required = false) String productKey,
            @RequestHeader(value = "deviceId", required = false) String deviceId,
            @RequestHeader(value = "sign", required = false) String sign,
            @RequestHeader(value = "ts", required = false) Long ts,
            @RequestPart("file") MultipartFile audioFile,
            @RequestParam(value = "format", defaultValue = "mp3") String format,
            @RequestParam(value = "data", defaultValue = "{}") String data,
            @RequestParam(value = "sn", defaultValue = "xxx") String sn) {
        SseEmitter emitter = new SseEmitter();
        try {
            if(!signUtil.checkSign(deviceId,ts,sign)){
                emitter.send(CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED));
                emitter.complete();
                return emitter;
            }
            byte[] audioBytes = audioFile.getBytes();
            // 异步调用 asrChat，不等待结果
            CompletableFuture.runAsync(() -> {
                try {
                    asrChatClient.asrChat(sn, audioBytes, data, emitter);
                } catch (Exception e) {
                    log.error("处理音频文件失败", e);
                    CommonResult res = CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), "处理音频文件失败");
                    try {
                        emitter.send(JSON.toJSONString(res));
                    } catch (IOException ex) {
                        log.error("发送错误信息失败", ex);
                    } finally {
                        emitter.complete();
                    }
                }
            });
            return emitter;
        } catch (Exception e) {
            log.error("初始化处理失败", e);
            emitter.completeWithError(e);
            return emitter;
        }
    }

    @Resource
    private BaiduInsideRcClient insideRcClient;

    @PostMapping(value = "/v1/rc-chat", produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE})
    public Publisher<?> chat(
            @RequestHeader(value = "deviceNo", required = true) String deviceNo,
            @RequestHeader(value = "productId", required = true) String productId,
            @RequestHeader(value = "productKey", required = true) String productKey,
            @RequestHeader(value = "deviceId", required = true) String deviceId,
            @RequestHeader(value = "sign", required = true) String sign,
            @RequestHeader(value = "ts", required = false) Long ts,
            @RequestBody String query) {
        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        log.info("receive rcCaht {} {}",JSON.toJSONString(aiCommonHeader),query);
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return Mono.just(CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED));
        }
        return insideRcClient.chat(aiCommonHeader,query);
    }

    @PostMapping(value = "/v1/note-summary", produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE})
    public Publisher<?> noteSummary(
            @RequestHeader(value = "deviceNo", required = true) String deviceNo,
            @RequestHeader(value = "productId", required = true) String productId,
            @RequestHeader(value = "productKey", required = true) String productKey,
            @RequestHeader(value = "deviceId", required = true) String deviceId,
            @RequestHeader(value = "sign", required = true) String sign,
            @RequestHeader(value = "ts", required = false) Long ts,
            @RequestBody NoteSummaryReqVo reqVo) {
        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        log.info("receive rcCaht {} {}",JSON.toJSONString(aiCommonHeader),reqVo);
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return Mono.just(CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED));
        }
        return insideRcClient.noteSummary(aiCommonHeader,reqVo);
    }

    @PostMapping(value = "/v1/sync-draw", produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE})
    public Mono<CommonResult<BaiduDrawImageClient.AigcResponse>> chat(
            @RequestHeader(value = "deviceNo", required = true) String deviceNo,
            @RequestHeader(value = "productId", required = true) String productId,
            @RequestHeader(value = "productKey", required = true) String productKey,
            @RequestHeader(value = "deviceId", required = true) String deviceId,
            @RequestHeader(value = "sign", required = true) String sign,
            @RequestHeader(value = "ts", required = false) Long ts,
            @RequestBody BaiduDrawImageClient.GenerateParams params) {
        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        log.info("receive sync-draw {} {}",JSON.toJSONString(aiCommonHeader),JSON.toJSONString(params));
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return Mono.just(CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED));
        }
        return insideRcClient.draw(aiCommonHeader,params);
    }


    @Resource
    private BaiduImageClassfyClient imageClassfyClient;

    @PostMapping(value = "/v1/image-classify", produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE})
    public CommonResult<JSONObject> imageClassify(
            @RequestHeader(value = "deviceNo", required = true) String deviceNo,
            @RequestHeader(value = "productId", required = true) String productId,
            @RequestHeader(value = "productKey", required = true) String productKey,
            @RequestHeader(value = "deviceId", required = true) String deviceId,
            @RequestHeader(value = "sign", required = true) String sign,
            @RequestHeader(value = "ts", required = false) Long ts,
            @RequestBody ImageClassifyReq params) {
        long start =System.currentTimeMillis();
        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        log.info("receive image-classify {}",JSON.toJSONString(aiCommonHeader));
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        JSONObject ret = imageClassfyClient.imageClassfy(params.getImageBase64(), params.getImageUrl(), params.getBaikeNum());
        long end = System.currentTimeMillis();
        CommonResult result =null;
        if (ret.getInteger("error_code") != null) {
            result= CommonResult.result(ret.getInteger("error_code"), ret.getString("error_msg"), ret);
        } else {
            result= CommonResult.success(ret);
        }
        String retStr= JSON.toJSONString(result);
        TaskExecutorConfig.getInstance().execute(() -> {
            LogHistory logHistory=new LogHistory();
            logHistory.setDeviceId(deviceId);
            logHistory.setDeviceNo(deviceNo);
            logHistory.setProductId(productId);
            logHistory.setTraceId(String.valueOf(ts));
            logHistory.setApiName(AiApiNameConstants.IMAGE_CLASSIFY);
            logHistory.setCreateTime(new Date());
            logHistory.setId(IdUtil.objectId());
            byte[] image= Base64.getDecoder().decode(params.getImageBase64());
            String path =ProductUtils.getLogFilePath(productId,deviceId);
            String fileName=start+"_imageClassfy.png";
            FileRespDTO fileRespDTO=fileApi.createFileWithDetail(path,fileName,fileName,image);
            logHistory.setReqFile(new LogHistory.FileRecord(start,fileRespDTO.getSize(),fileRespDTO.getType(),fileRespDTO.getUrl()));
            params.setImageBase64(fileRespDTO.getUrl());
            logHistory.setReqMsgs(Lists.newArrayList(new LogHistory.Message(start,JSON.toJSONString(params))));
            logHistory.setRespMsgs(Lists.newArrayList(new LogHistory.Message(end,retStr)));
            logHistory.setTotalCost(end-start);
            logHistoryRepository.save(logHistory);
        });
        return result;
    }


    @Resource
    private BaiduSpeechFileTransferClient speechFileTransferClient;

    @PostMapping(value = "/v1/speech-file-transfer", produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE})
    public CommonResult<SpeechFileTransferResp> speechFileTransfer(
            @RequestHeader(value = "deviceNo", required = true) String deviceNo,
            @RequestHeader(value = "productId", required = true) String productId,
            @RequestHeader(value = "productKey", required = true) String productKey,
            @RequestHeader(value = "deviceId", required = true) String deviceId,
            @RequestHeader(value = "sign", required = true) String sign,
            @RequestHeader(value = "ts", required = false) Long ts,
            @RequestBody SpeechFileTransferReq speechFileTransferReq) {
        SpeechFileTransferResp ret = null;
        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        log.info("receive speech-file-transfer {} {}",JSON.toJSONString(aiCommonHeader),JSON.toJSONString(speechFileTransferReq));
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        long start =System.currentTimeMillis();
        try {
            ret = speechFileTransferClient.transferSpeech(speechFileTransferReq);
            long end =System.currentTimeMillis();
            CommonResult commRet;
            if (ret.getError_code() != null) {
                commRet= CommonResult.error(ret.getError_code(), ret.getError_msg());
            }else{
                commRet =CommonResult.success(ret);
            }
            String comRetStr=JSON.toJSONString(commRet);
            TaskExecutorConfig.getInstance().execute(() -> {
                LogHistory logHistory=new LogHistory();
                logHistory.setDeviceId(deviceId);
                logHistory.setTraceId(String.valueOf(ts));
                logHistory.setApiName(AiApiNameConstants.SPEECH_FILE_TRANSFER);
                logHistory.setCreateTime(new Date());
                logHistory.setId(IdUtil.objectId());
                logHistory.setReqMsgs(Lists.newArrayList(new LogHistory.Message(start,JSON.toJSONString(speechFileTransferReq))));
                logHistory.setRespMsgs(Lists.newArrayList(new LogHistory.Message(end,comRetStr)));
                byte[] saveFile = HttpUtil.downloadBytes(speechFileTransferReq.getSpeech_url());
                saveFile = AudioUtil.addWavHeader(16000, saveFile);
                String path = ProductUtils.getLogFilePath(productId, deviceId);
                String fileName = start + "_speechFileTransfer.wav";
                FileRespDTO fileRespDTO = fileApi.createFileWithDetail(path, fileName, fileName, saveFile);
                logHistory.setReqFile(new LogHistory.FileRecord(start, fileRespDTO.getSize(), fileRespDTO.getType(), fileRespDTO.getUrl()));
                logHistory.setTotalCost(end-start);
                logHistory.setProductId(productId);
                logHistory.setDeviceNo(deviceNo);
                logHistoryRepository.save(logHistory);
            });
            return commRet;
        } catch (Exception e) {
            log.error("exception on speech-file-transfer",e);
            return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping(value = "/v1/speech-file-transfer-query", produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE})
    public CommonResult<SpeechFileTransferQueryResp> speechFileTransferQuery(
            @RequestHeader(value = "deviceNo", required = true) String deviceNo,
            @RequestHeader(value = "productId", required = true) String productId,
            @RequestHeader(value = "productKey", required = true) String productKey,
            @RequestHeader(value = "deviceId", required = true) String deviceId,
            @RequestHeader(value = "sign", required = true) String sign,
            @RequestHeader(value = "ts", required = false) Long ts,
            @RequestBody SpeechFileTransferQueryReq req) {
        SpeechFileTransferQueryResp ret = null;
        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        log.info("receive speech-file-transfer-query {} {}",JSON.toJSONString(aiCommonHeader),req.getTask_ids());

        try {
            ret = speechFileTransferClient.queryTransferSpeechResult(req);
            if (ret.getError_code() != null) {
                return CommonResult.error(ret.getError_code(), ret.getError_msg());
            }
            return CommonResult.success(ret);
        } catch (Exception e) {
            return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
        }
    }


    @Resource
    private FileApi fileApi;

    @PostMapping("/v1/get-upload-audio-url")
    @ApiOperation("生成上传文件url")
    @OperateLog(enable = false)
    public CommonResult<AudioUploadUrlResp> uploadLogFile(@RequestHeader(value = "deviceNo", required = true) String deviceNo,
                                                          @RequestHeader(value = "productId", required = true) String productId,
                                                          @RequestHeader(value = "productKey", required = true) String productKey,
                                                          @RequestHeader(value = "deviceId", required = true) String deviceId,
                                                          @RequestHeader(value = "sign", required = true) String sign,
                                                          @RequestHeader(value = "ts", required = false) Long ts,
                                                          @RequestBody @Validated AiGenUploadUrlReq reqVO) throws Exception {
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        String suffix = FileNameUtil.getSuffix(reqVO.getFilename());
        String prefix = "device/ai/audio/" + productKey + "/" + deviceNo + "/";
        String uuid = IdUtil.fastSimpleUUID();
        String path = prefix + uuid + "_" + reqVO.getFilename();
        GenPresignedUrlDTO genPresignedUrlDTO = fileApi.genPresignedUrl(path, new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000L));
        String downloadUrl = fileApi.greneratePresignedDownloadUrl(genPresignedUrlDTO.getRealUrl(), new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L));
        AudioUploadUrlResp resp = new AudioUploadUrlResp();
        resp.setUploadUrl(genPresignedUrlDTO.getPreSignedUrl());
        resp.setAccessUrl(downloadUrl);
        log.info("gen upload log file url {}", genPresignedUrlDTO.getPreSignedUrl());
        return success(resp);
    }


    @Resource
    private BaiduImageTranslateClient imageTranslateClient;

    @PostMapping("/v1/image-translate")
    @ApiOperation("图片翻译接口")
    @OperateLog(enable = false)
    public CommonResult<?> imageTranslate(@RequestHeader(value = "deviceNo", required = true) String deviceNo,
                                                           @RequestHeader(value = "productId", required = true) String productId,
                                                           @RequestHeader(value = "productKey", required = true) String productKey,
                                                           @RequestHeader(value = "deviceId", required = true) String deviceId,
                                                           @RequestHeader(value = "sign", required = true) String sign,
                                                           @RequestHeader(value = "ts", required = false) Long ts,
                                                           @RequestBody @Validated BaiduImageTranslateReqVo reqVO) throws Exception {

        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        log.info("receive image-translate {}",JSON.toJSONString(aiCommonHeader));
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        long start =System.currentTimeMillis();
        CommonResult<com.alibaba.fastjson2.JSONObject>result = imageTranslateClient.translate(reqVO);
        long end =System.currentTimeMillis();
        TaskExecutorConfig.getInstance().execute(() -> {
            LogHistory logHistory=new LogHistory();
            logHistory.setTraceId(String.valueOf(ts));
            logHistory.setApiName(AiApiNameConstants.IMAGE_TRANSLATE);
            logHistory.setCreateTime(new Date());

            logHistory.setTotalCost(end-start);
            logHistory.setProductId(productId);
            logHistory.setDeviceNo(deviceNo);
            logHistory.setDeviceId(deviceId);
            logHistory.setTraceId(String.valueOf(ts));
            logHistory.setId(IdUtil.objectId());
            byte[] image= Base64.getDecoder().decode(reqVO.getImageBase64());
            String path =ProductUtils.getLogFilePath(productId,deviceId);
            String fileName=start+"_imageTranslate.png";
            FileRespDTO fileRespDTO=fileApi.createFileWithDetail(path,fileName,fileName,image);
            logHistory.setReqFile(new LogHistory.FileRecord(start,fileRespDTO.getSize(),fileRespDTO.getType(),fileRespDTO.getUrl()));
            reqVO.setImageBase64(fileRespDTO.getUrl());
            logHistory.setReqMsgs(Lists.newArrayList(new LogHistory.Message(start,JSON.toJSONString(reqVO))));
            if(result.getData()!=null && StringUtils.isNotBlank(result.getData().getString("pasteImg"))){
                String pasteImage= result.getData().getString("pasteImg");
                String pasteImg=start+"_imageTranslate_pasteImg.png";
                String pastePath =ProductUtils.getLogFilePath(productId,deviceId);
                byte[] pasteBase64= Base64.getDecoder().decode(pasteImage);
                FileRespDTO pasteFile=fileApi.createFileWithDetail(pastePath,pasteImg,pasteImg,pasteBase64);
                result.getData().put("pasteImg",pasteFile.getUrl());
                logHistory.setRespFile(new LogHistory.FileRecord(start,pasteFile.getSize(),pasteFile.getType(),pasteFile.getUrl()));
            }
            logHistory.setRespMsgs(Lists.newArrayList(new LogHistory.Message(end,JSON.toJSONString(result))));
            logHistoryRepository.save(logHistory);
        });
        return result;
    }


    @Resource
    private BaiduOcrClient baiduOcrClient;
    @PostMapping("/v1/ocr")
    @ApiOperation("ocr识别接口")
    @OperateLog(enable = false)
    public CommonResult<JSONObject> imageTranslate(@RequestHeader(value = "deviceNo", required = true) String deviceNo,
                                                           @RequestHeader(value = "productId", required = true) String productId,
                                                           @RequestHeader(value = "productKey", required = true) String productKey,
                                                           @RequestHeader(value = "deviceId", required = true) String deviceId,
                                                           @RequestHeader(value = "sign", required = true) String sign,
                                                           @RequestHeader(value = "ts", required = false) Long ts,
                                                           @RequestBody @Validated BaiduOcrReqVo reqVO)  {
        long start =System.currentTimeMillis();
        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        log.info("receive ocr {}",JSON.toJSONString(aiCommonHeader));
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        JSONObject result=baiduOcrClient.orc(reqVO);
        long end =System.currentTimeMillis();
        TaskExecutorConfig.getInstance().execute(() -> {
            LogHistory logHistory=new LogHistory();
            logHistory.setTraceId(String.valueOf(ts));
            logHistory.setApiName(AiApiNameConstants.OCR);
            logHistory.setCreateTime(new Date());
            logHistory.setRespMsgs(Lists.newArrayList(new LogHistory.Message(end,JSON.toJSONString(result))));
            logHistory.setTotalCost(end-start);
            logHistory.setProductId(productId);
            logHistory.setDeviceNo(deviceNo);
            logHistory.setDeviceId(deviceId);
            logHistory.setTraceId(String.valueOf(ts));
            logHistory.setId(IdUtil.objectId());
            byte[] image= Base64.getDecoder().decode(reqVO.getImage());
            String path =ProductUtils.getLogFilePath(productId,deviceId);
            String fileName=start+"_ocr.png";
            FileRespDTO fileRespDTO=fileApi.createFileWithDetail(path,fileName,fileName,image);
            logHistory.setReqFile(new LogHistory.FileRecord(start,fileRespDTO.getSize(),fileRespDTO.getType(),fileRespDTO.getUrl()));
            reqVO.setImage(fileRespDTO.getUrl());
            logHistory.setReqMsgs(Lists.newArrayList(new LogHistory.Message(start,JSON.toJSONString(reqVO))));
            logHistoryRepository.save(logHistory);
        });
        return CommonResult.success(result);
    }

    @Resource
    private BaiduWordQueryClient wordQueryClient;
    @PostMapping("/v1/word-query")
    @ApiOperation("ocr识别接口")
    @OperateLog(enable = false)
    public CommonResult<JSONObject> wordQuery(@RequestHeader(value = "deviceNo", required = true) String deviceNo,
                                                   @RequestHeader(value = "productId", required = true) String productId,
                                                   @RequestHeader(value = "productKey", required = true) String productKey,
                                                   @RequestHeader(value = "deviceId", required = true) String deviceId,
                                                   @RequestHeader(value = "sign", required = true) String sign,
                                                   @RequestHeader(value = "ts", required = false) Long ts,
                                                   @RequestBody @Validated BaiduWordReqVo reqVO) {
        long start =System.currentTimeMillis();
        AiCommonHeader aiCommonHeader=buildAiCommonHeader(deviceNo,productId,productKey,deviceId,sign,ts);
        log.info("receive word-query {} {}",JSON.toJSONString(aiCommonHeader),JSON.toJSONString(reqVO));
        if(!signUtil.checkSign(deviceId,ts,sign)){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        CommonResult result= wordQueryClient.queryWord(reqVO.getWord(),deviceId);
        long end=System.currentTimeMillis();
        TaskExecutorConfig.getInstance().execute(() -> {
            LogHistory logHistory=new LogHistory();
            logHistory.setTraceId(String.valueOf(ts));
            logHistory.setApiName(AiApiNameConstants.WORD_QUERY);
            logHistory.setCreateTime(new Date());
            logHistory.setRespMsgs(Lists.newArrayList(new LogHistory.Message(end,JSON.toJSONString(result))));
            logHistory.setTotalCost(end-start);
            logHistory.setProductId(productId);
            logHistory.setDeviceNo(deviceNo);
            logHistory.setDeviceId(deviceId);
            logHistory.setTraceId(String.valueOf(ts));
            logHistory.setId(IdUtil.objectId());

            logHistory.setReqMsgs(Lists.newArrayList(new LogHistory.Message(start,JSON.toJSONString(reqVO))));
            logHistoryRepository.save(logHistory);
        });
        return result;
    }
}
