package cn.iocoder.yudao.module.iot.controller.open.hw.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class ConverationChatVo {
    @NotBlank(message = "查询不能为空")
    @Size(max = 1000, message = "对话内容不能超过1000个字符")
    private String query;
    private JSONObject extra;
    private String conversation_id;
    private Boolean stream=true;
    private String botId;
    private String regen_message_id;
    private Boolean tts=false;
}
