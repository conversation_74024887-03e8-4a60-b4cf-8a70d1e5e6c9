package cn.iocoder.yudao.module.iot.controller.open.ai;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.encrypt.EncryptUtil;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.iot.config.JiutianProperties;
import cn.iocoder.yudao.module.iot.controller.open.ai.service.AiService;
import cn.iocoder.yudao.module.iot.controller.open.ai.vo.*;
import cn.iocoder.yudao.module.system.config.AiProperties;
import cn.iocoder.yudao.module.system.service.dify.vo.DifyAppDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Api(tags = "用户 APP - 通知公告")
@RestController
@Slf4j
@RequestMapping("/open-api/ai")
public class AiController {
    @Resource
    private JiutianProperties jiutianProperties;
    @Resource
    private WebClient webClient;
    @PostMapping(value = "/jiutian/v1/chat/completions", produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE,MediaType.ALL_VALUE})
    public Publisher<?> chatCompletions(
            @RequestBody ChatRequest request, ServletResponse response,
            @RequestHeader("Authorization") String authorization) {
        if(request.isStream()){
            response.setContentType(MediaType.TEXT_EVENT_STREAM_VALUE);
        }else{
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        }

        if(jiutianProperties.getYd75bToken().equalsIgnoreCase(authorization)){
            authorization = jiutianProperties.getYd75bToken();
        }

        return aiService.chat(null,request,authorization);
    }

    @PostMapping(value = "/yd-jiutian/v1/chat/completions", produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE,MediaType.ALL_VALUE})
    public Publisher<?> chatCompletionsV2(
            @RequestBody ChatRequest request, ServletResponse response,
            @RequestHeader("Authorization") String authorization) {
        if(request.isStream()){
            response.setContentType(MediaType.TEXT_EVENT_STREAM_VALUE);
        }else{
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        }

        return aiService.ydChat(null,request,authorization);
    }


    @PostMapping("/image/image-translate")
    public CommonResult<AppImageTranslateRespVO> imageTranslate(@RequestHeader(name = "ts", required = true) String ts,
                                                                @RequestHeader(name = "token", required = true) String Authorization,
                                                                @RequestBody AppImageTranslateReqVO reqVO) {
        log.info("请求图片翻译{}", JSON.toJSON(reqVO));
        String apiKey = aiProperties.getInnerApiKey();
        String auth = EncryptUtil.md5(apiKey + ts);
        if (!auth.equals(Authorization)) {
            log.info("认证失败Authorization={} ts={} realAuth={}", Authorization, ts, auth);
            return CommonResult.error(401, "认证失败");
        }

        return aiService.imageTranslate(reqVO);
    }


    @PostMapping("/text/text-translate")
    public CommonResult<TextTranslateRespVO> translate(@RequestHeader(name = "ts", required = true) String ts,
                                                       @RequestHeader(name = "token", required = true) String Authorization, @RequestBody TextTranslateReqVO reqVO) {
        log.info("请求图片翻译{}", JSON.toJSON(reqVO));
        String apiKey = aiProperties.getInnerApiKey();
        String auth = EncryptUtil.md5(apiKey + ts);
        if (!auth.equals(Authorization)) {
            log.info("认证失败Authorization={} ts={} realAuth={}", Authorization, ts, auth);
            return CommonResult.error(401, "认证失败");
        }

        return aiService.textTranslate(reqVO);

    }

    @Resource
    private AiProperties aiProperties;

    @PostMapping("/text/text2-image")
    public CommonResult<String> text2Image(@RequestHeader(name = "Authorization", required = true) String auth,
                                           @RequestBody Text2ImageReqVO reqVO) {
        return aiService.text2Image(auth, reqVO);
    }

    @Resource
    private AiService aiService;

    @GetMapping("/text/text-to-audio")
    public SseEmitter text2Audio(@RequestHeader(name = "Authorization", required = false) String Authorization,
                                 @RequestHeader(name = "ts", required = true) String ts,
                                 @RequestParam String text, @RequestParam(defaultValue = "siyue", required = false) String voice,
                                 HttpServletResponse response) throws Exception {
        log.info("请求文字转语音{} {} {}", Authorization, text, voice);
        String apiKey = aiProperties.getInnerApiKey();
        SseEmitter emitter = new SseEmitter();
        String auth = EncryptUtil.md5(apiKey + ts);
        if (!auth.equals(Authorization)) {
            log.info("认证失败Authorization={} ts={} realAuth={}", Authorization, ts, auth);
            emitter.send("认证失败");
            emitter.complete();
            return emitter;
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("X-Accel-Buffering", "no");
        aiService.text2AudioStream(text, voice, emitter);
        return emitter;

    }

    @PostMapping("/text/audio-to-text")
    public CommonResult<String> audio2Text(@RequestHeader(name = "Authorization", required = true) String Authorization, @RequestHeader(name = "ts", required = true) String ts, @RequestBody AudioToTextReqVO reqVO) {
        log.info("请求音频转文字{}", JSON.toJSON(reqVO));
        String apiKey = aiProperties.getInnerApiKey();
        String auth = EncryptUtil.md5(apiKey + ts);
        if (!auth.equals(Authorization)) {
            log.info("认证失败Authorization={} ts={} realAuth={}", Authorization, ts, auth);
            return CommonResult.error(401, "认证失败");
        }
        log.info("请求语音转文字{} ts {}", Authorization, ts);
        return aiService.audio2Text(null,reqVO);
    }

    @PostMapping("/search/news")
    public CommonResult<List<NewsSerchRespVO>> searchNews(@RequestHeader(name = "Authorization", required = true) String Authorization, @RequestBody NewsSearchReqVO reqVO) {
        // 构建请求体
        log.info("search news {} {}", Authorization, JSON.toJSONString(reqVO));
        return aiService.searchNews(Authorization, reqVO);
    }


    public static void main(String[] args) throws TencentCloudSDKException {


    }
}
