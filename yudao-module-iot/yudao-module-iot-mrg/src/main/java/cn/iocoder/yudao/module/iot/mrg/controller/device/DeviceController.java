package cn.iocoder.yudao.module.iot.mrg.controller.device;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.encrypt.EncryptUtil;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.module.iot.cache.redis.DevicePropertyCache;
import cn.iocoder.yudao.module.iot.cache.redis.StrategyUpgradeRecRedisCache;
import cn.iocoder.yudao.module.iot.dal.dataobject.device.DeviceDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.firmware.FirmwareDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.fotastrategy.FotaProgressDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.fotastrategy.FotaStrategyDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.fotastrategy.StrategyUpgradeRecDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.mqttnode.MqttNodeDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.product.ProductDO;
import cn.iocoder.yudao.module.iot.dal.mongo.FotaProgressMapper;
import cn.iocoder.yudao.module.iot.dal.mongo.FotaProgressRepository;
import cn.iocoder.yudao.module.iot.dal.mongo.ReactiveDeviceRepository;
import cn.iocoder.yudao.module.iot.dal.mongo.StrategyUpgradeRecRepository;
import cn.iocoder.yudao.module.iot.dal.mysql.firmware.FirmwareMapper;
import cn.iocoder.yudao.module.iot.dto.mrg.DeviceCheckFotaRespDTO;
import cn.iocoder.yudao.module.iot.dto.mrg.DeviceInfoDTO;
import cn.iocoder.yudao.module.iot.dto.mrg.ThingModelSendMsgDTO;
import cn.iocoder.yudao.module.iot.enums.device.DeviceStatusEnum;
import cn.iocoder.yudao.module.iot.enums.fota.FirmwareStatusEnum;
import cn.iocoder.yudao.module.iot.enums.fota.FotaStrategyUpgradeTypeEnum;
import cn.iocoder.yudao.module.iot.enums.fota.FotaUpgradeStatusEnum;
import cn.iocoder.yudao.module.iot.enums.mqttnode.MqttNodeStatus;
import cn.iocoder.yudao.module.iot.enums.msg.MsgMethodEnums;
import cn.iocoder.yudao.module.iot.metrics.DeviceActiveCounter;
import cn.iocoder.yudao.module.iot.metrics.DeviceLastReqCounter;
import cn.iocoder.yudao.module.iot.mrg.config.AiProperties;
import cn.iocoder.yudao.module.iot.mrg.config.DeviceRetryStrategyProperties;
import cn.iocoder.yudao.module.iot.mrg.config.MrgProperties;
import cn.iocoder.yudao.module.iot.mrg.consumer.DeviceReportComsumer;
import cn.iocoder.yudao.module.iot.mrg.controller.device.vo.*;
import cn.iocoder.yudao.module.iot.mrg.dto.DeviceOtaProgressDTO;
import cn.iocoder.yudao.module.iot.mrg.service.device.DeviceService;
import cn.iocoder.yudao.module.iot.mrg.service.device.ForwardService;
import cn.iocoder.yudao.module.iot.mrg.service.device.FotaStrategyService;
import cn.iocoder.yudao.module.iot.mrg.service.device.ProductService;
import cn.iocoder.yudao.module.iot.mrg.service.mqtt.MqttNodeService;
import cn.iocoder.yudao.module.iot.mrg.threadpool.TaskExecutorFactory;
import cn.iocoder.yudao.module.iot.mrg.util.IpUtil;
import cn.iocoder.yudao.module.iot.mrg.util.MqttMsgUtil;
import com.alibaba.fastjson2.JSON;
import com.github.yitter.idgen.YitIdHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static cn.iocoder.yudao.module.iot.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.iot.mrg.handler.DeviceOtaProgressHandler.PROGRESS_100;

/**
 * <AUTHOR>
 * @email: <EMAIL>
 * @date: 2022/7/14 11:06
 */
@Slf4j
@RestController
public class DeviceController {
    public static final int FACTORY_VERIFICATION_MAX_COUNT = 100;
    public static final String MQTT_URL_CONFIG_PROP = "mqttUrl";
    public static final String CONFIG_PROP_MQTT_MSG_ENCRYPT = "mqttMsgEncrypt";
    @Resource
    private DeviceService deviceService;
    @Resource
    private MqttNodeService mqttNodeService;
    @Resource
    private DevicePropertyCache devicePropertyCache;
    @Resource
    private ProductService productService;
    @Resource
    private DeviceRetryStrategyProperties deviceRetryStrategyProperties;
    @Resource
    private FotaStrategyService fotaStrategyService;

    @Resource
    private ForwardService forwardService;

    @Resource
    private AiProperties aiProperties;
    /**
     * APP限制最多上传10M的文件
     */
    private static final long FILE_SIZE_LIMIT = 1024 * 1024 * 10L;

    @PostMapping("/v2/customer/device/secret/info")
    public AiCommonResult<Map> register(ServerHttpRequest request,
                                       @RequestBody AiDeviceReqVO aiDeviceReqVO) {
        AiCommonResult<Map> result=new AiCommonResult<Map>();
        DeviceDO device = deviceService.find(aiDeviceReqVO.getProductKey(), aiDeviceReqVO.getDeviceNo());
        if(device.getId()!=null){
            log.info("register {} {} {}",device.getProductId(),aiDeviceReqVO.getProductId(),Objects.equals(device.getProductId(),aiDeviceReqVO.getProductId()));
        }
        if (device.getId()==null || (!Objects.equals(device.getProductId(),aiDeviceReqVO.getProductId()) || !Objects.equals(device.getProductKey(),aiDeviceReqVO.getProductKey()))){
            result.setCode("500");
            result.setSuccess(false);
            result.setMessage("设备或产品不存在");

        }else {
            Map deviceMap = new HashMap();
            deviceMap.put("deviceId", String.valueOf(device.getId()));
            deviceMap.put("deviceNo", device.getSn());
            deviceMap.put("productId", String.valueOf(device.getProductId()));
            deviceMap.put("deviceSecret", device.getSecret());
            result.setData(deviceMap);
            if(device.getActivateTime()==null){
                device.setIp(IpUtil.getIPAddress(request));
                device.setActivateTime(new Date());
                device.setOnlineTime(new Date());
                device.setStatus(DeviceStatusEnum.ONLINE.getStatus());
                deviceService.update(device);
            }
        }
        forwardService.forwardRequestAsync(aiProperties.getDeviceInfoUrl(),JSON.toJSONString(aiDeviceReqVO), String.valueOf(request.getHeaders().get("X-Forwarded-For")));
        return result;
    }

    @PostMapping("/v2/customer/device/report")
    public AiCommonResult<Map> report(ServerHttpRequest request,
                                     @RequestBody AiDeviceReportVO aiDeviceReportVO) {
        AiCommonResult<Map> result=new AiCommonResult<Map>();

        DeviceDO device = deviceService.findById(aiDeviceReportVO.getDeviceId());
        if(device.getId()!=null){
            log.info("report {} {} {}",device.getProductId(),aiDeviceReportVO.getProductId(),Objects.equals(device.getProductId(),aiDeviceReportVO.getProductId()));
        }
        if (device.getId()==null || (!Objects.equals(device.getProductId(),aiDeviceReportVO.getProductId())  ||  !Objects.equals(device.getProductKey(),aiDeviceReportVO.getProductKey()))){
            result.setCode("500");
            result.setSuccess(false);
            result.setMessage("设备或产品不存在");
        }else {
            DeviceActiveCounter.record(String.valueOf(device.getId()));
            Map deviceMap = new HashMap();
            deviceMap.put("deviceId", String.valueOf(device.getId()));
            deviceMap.put("protocolTypeTime", 0);
            result.setData(deviceMap);
            device.setIp(IpUtil.getIPAddress(request));
            device.setStatus(DeviceStatusEnum.ONLINE.getStatus());
            device.setOnlineTime(new Date());
            device.setUpdateTime(new Date());
            //TODO 补充信息
            if(aiDeviceReportVO.getParams()!=null){
                Map map=aiDeviceReportVO.getParams();
                device.setImei(String.valueOf(map.get("imei")));
                device.setCmei(String.valueOf(map.get("cmei")));
                device.setFwVersion(String.valueOf(map.get("firmwareVersion")));
                device.setSdkVersion(String.valueOf(map.get("sdkVersion")));
                device.setMac(String.valueOf(map.get("mac")));
            }
            deviceService.updateOnline(device);
        }
        forwardService.forwardRequestAsync(aiProperties.getDeviceReportUrl(),JSON.toJSONString(aiDeviceReportVO), String.valueOf(request.getHeaders().get("X-Forwarded-For")));
        return result;
    }

    @PostMapping("/device-api/device/register")
    public Mono<CommonResult> register(ServerHttpRequest request, @RequestHeader(value = "sign", required = true) String sign,
                                       @RequestHeader(value = "version", required = false) String version,
                                       @RequestHeader(value = "signMethod", required = false) String signMethod,
                                       @RequestHeader(value = "dynamic", required = false, defaultValue = "false") Boolean dynamic,
                                       @RequestBody DeviceRegisterReqVO deviceRegisterReqVO) {
        log.info("recv device register [sign={},signMethod={},dynamic={}，version={}] {}", sign, signMethod, dynamic, version, JSON.toJSONString(deviceRegisterReqVO));
        long nanoStart = System.nanoTime();
        String ip = IpUtil.getIPAddress(request);

        Mono<DeviceDO> device = deviceService.findReactive(deviceRegisterReqVO.getProductKey(), deviceRegisterReqVO.getSn());
        return device.defaultIfEmpty(new DeviceDO())
                .flatMap(deviceDO -> {
                    if (deviceDO == null || deviceDO.getId() == null) {
                        log.warn("device {} not exist");
                        return Mono.just(CommonResult.error(DEVICE_NOT_EXISTS));
                    }

                    if (!Objects.equals(deviceDO.getIp(), ip)) {
                        log.info("register device {} ip change from {} to {}", deviceDO.getSn(), deviceDO.getIp(), ip);
                        deviceDO.setIp(ip);
                        DeviceDO tmp = new DeviceDO();
                        tmp.setId(deviceDO.getId());
                        tmp.setProductKey(deviceDO.getProductKey());
                        tmp.setSn(deviceDO.getSn());
                        tmp.setIp(ip);
                        deviceService.updateOnline(tmp);
                    }

                    if (dynamic && deviceDO.getHasRegistered() != null && deviceDO.getHasRegistered()) {
                        log.warn("device {} hasRegistered", deviceDO.getId());
                        return Mono.just(CommonResult.error(DEVICE_HAS_REGISTERED));
                    }
                    ProductDO productDO = productService.find(deviceRegisterReqVO.getProductKey());
                    if (productDO == null) {
                        log.warn("device {} register fail,product {} not exists", deviceRegisterReqVO.getSn(), deviceRegisterReqVO.getProductKey());
                        return Mono.just(CommonResult.error(PRODUCT_NOT_EXISTS));
                    }
                    String encrySecret = deviceDO.getSecret();
                    if (dynamic) {
                        encrySecret = productDO.getSecret();
                    }
                    String valueToDigest = deviceRegisterReqVO.getSn() + "_" + deviceRegisterReqVO.getProductKey() + "_" + deviceRegisterReqVO.getTimestamp();
                    String encrySign = EncryptUtil.hmacSha1(valueToDigest, encrySecret);
                    if (!StringUtils.equals(encrySign, sign) && mrgProperties.getCheckSign()) {
                        log.info("device {} register fail,sign {} not eq {}", deviceRegisterReqVO.getSn(), sign, encrySign);
                        return Mono.just(CommonResult.error(UNAUTHORIZED));
                    }
                    if (Objects.isNull(deviceDO.getActivateTime())) {
                        log.warn("register update activate time {}", deviceDO.getId());
                        Date activateTime = new Date();
                        deviceDO.setActivateTime(activateTime);
                        DeviceDO tmp = new DeviceDO();
                        tmp.setId(deviceDO.getId());
                        tmp.setProductKey(deviceDO.getProductKey());
                        tmp.setSn(deviceDO.getSn());
                        tmp.setActivateTime(activateTime);
                        deviceService.update(tmp);
                    }
                    MqttConnectionRespVO mqttConnectionRespVO = new MqttConnectionRespVO();
                    mqttConnectionRespVO.setMqttMsgEncrypt(DeviceReportComsumer.isDeviceMsgUnEncrypt(deviceDO));
                    if (dynamic) {
                        mqttConnectionRespVO.setSecret(deviceDO.getSecret());
                    }

                    String url = getMqttConnectionUrl(deviceDO);
                    if (url == null) {
                        log.error("device {} register fail,can not get mqtt url", deviceDO.getSn());
                        return Mono.just(CommonResult.error(INTERNAL_SERVER_ERROR));
                    }
                    mqttConnectionRespVO.setConnectionUrl(url);
                    Long cost = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - nanoStart);
                    log.info("device register resp {} cost {}", mqttConnectionRespVO, cost);
                    return Mono.just(CommonResult.success(mqttConnectionRespVO));
                });
    }

    @PostMapping("/device-api/device/confirm-register")
    public Mono<CommonResult> confirmRegister(@RequestHeader(value = "sign", required = true) String sign,
                                              @RequestHeader(value = "version", required = false) String version,
                                              @RequestHeader(value = "signMethod", required = false) String signMethod,
                                              @RequestBody DeviceRegisterReqVO deviceRegisterReqVO) {
        log.info("recv device confirm-register [sign={},signMethod={}]{},version={}] {}", sign, signMethod, version, JSON.toJSONString(deviceRegisterReqVO));
        DeviceDO deviceDO = deviceService.find(deviceRegisterReqVO.getProductKey(), deviceRegisterReqVO.getSn());
        if (deviceDO == null || deviceDO.getId() == null) {
            return Mono.just(CommonResult.error(DEVICE_NOT_EXISTS));
        }
        ProductDO productDO = productService.find(deviceRegisterReqVO.getProductKey());
        if (productDO == null) {
            return Mono.just(CommonResult.error(PRODUCT_NOT_EXISTS));
        }
        if (deviceDO.getHasRegistered() != null && deviceDO.getHasRegistered()) {
            return Mono.just(CommonResult.error(DEVICE_HAS_REGISTERED));
        }
        String encryptKey = deviceRegisterReqVO.getSn() + "_" + deviceRegisterReqVO.getProductKey() + "_" + deviceRegisterReqVO.getTimestamp();
        String encrySign = EncryptUtil.hmacSha1(encryptKey, deviceDO.getSecret());
        if (!StringUtils.equals(encrySign, sign)) {
            log.warn("device {} register fail,sign {} not eq {}", deviceRegisterReqVO.getSn(), sign, encrySign);
            return Mono.just(CommonResult.error(UNAUTHORIZED));
        }
        deviceDO.setHasRegistered(true);
        DeviceDO tmp = new DeviceDO();
        tmp.setHasRegistered(true);
        tmp.setId(deviceDO.getId());
        tmp.setProductKey(deviceDO.getProductKey());
        tmp.setSn(deviceDO.getSn());
        deviceService.update(tmp);
        return Mono.just(CommonResult.success(true));
    }

    @PostMapping("/device-api/device/report-runtime")
    public Mono<CommonResult> reportRunTime(@RequestHeader(value = "sign", required = true) String sign,
                                            @RequestHeader(value = "version", required = false) String version,
                                            @RequestHeader(value = "signMethod", required = false) String signMethod,
                                            @RequestBody DeviceRuntimeReqVO reqVO) {

        log.info("recv device reportRunTime [sign={},signMethod={},version={}] {}", sign, signMethod, version, JSON.toJSONString(reqVO));
        return deviceService.findReactive(reqVO.getProductKey(), reqVO.getSn())
                .defaultIfEmpty(new DeviceDO())
                .flatMap(deviceDO -> {
                    if (deviceDO.getId() == null) {
                        return Mono.just(CommonResult.error(DEVICE_NOT_EXISTS));
                    }
                    if (StringUtils.isBlank(deviceDO.getOwner())) {
                        log.info("设备{}未绑定APP,不存储runtime", deviceDO.getSn());
                        return Mono.just(CommonResult.success(true));
                    }
                    String valueToDigest = reqVO.getSn() + "_" + reqVO.getProductKey() + "_" + reqVO.getTimestamp();
                    String encrySign = EncryptUtil.hmacSha1(valueToDigest, deviceDO.getSecret());
                    if (!StringUtils.equals(encrySign, sign)) {
                        log.info("device {} report-runtime fail,sign {} not eq {}", reqVO.getSn(), sign, encrySign);
                        return Mono.just(CommonResult.error(UNAUTHORIZED));
                    }
                    TaskExecutorFactory.INSTANCE.getReportRunTimeTaskExecutor()
                            .execute(() -> {
                                deviceService.reportRunTime(deviceDO.getId(), reqVO);
                            });
                    return Mono.just(CommonResult.success(true));
                });
    }

    private static Object EMPTY = new Object();

    @PostMapping("/device-api/device/check-upgrade")
    public Mono<CommonResult> checkUpgrade(@RequestHeader(value = "sign", required = true) String sign,
                                           @RequestHeader(value = "version", required = false) String version,
                                           @RequestHeader(value = "signMethod", required = false) String signMethod,
                                           @RequestBody DeviceCheckUpgradeReqVO reqVO) {
        long nanoStart = System.nanoTime();
        Mono<DeviceDO> device = deviceService.findReactive(reqVO.getProductKey(), reqVO.getSn());
        return device.defaultIfEmpty(new DeviceDO())
                .flatMap(deviceDO -> {
                    if (deviceDO.getId() == null) {
                        return Mono.just(CommonResult.error(DEVICE_NOT_EXISTS));
                    }
                    DeviceLastReqCounter.putLastReq(deviceDO.getId(), System.currentTimeMillis());
                    String valueToDigest = reqVO.getSn() + "_" + reqVO.getProductKey() + "_" + reqVO.getTimestamp();
                    String encrySign = EncryptUtil.hmacSha1(valueToDigest, deviceDO.getSecret());
                    if (!StringUtils.equals(encrySign, sign) && mrgProperties.getCheckSign()) {
                        log.info("device {} check-upgrade fail,sign {} not eq {}", reqVO.getSn(), sign, encrySign);
                        return Mono.just(CommonResult.error(UNAUTHORIZED));
                    }
                    if ("RAX3000M-MTK.ZD.01.01.20230814".equals(reqVO.getFirmwareVersion())) {
                        reqVO.setFirmwareVersion("RAX3000M-MTK.ZD.01.01.20230812");
                        if ("RAX3000M_V1".equals(reqVO.getHardwareVersion()) && "RAX3000M_SL_V1".equals(deviceDO.getHdVersion())) {
                            reqVO.setHardwareVersion("RAX3000M_SL_V1");
                        }
                    }
                    checkAndUpdateDevice(deviceDO, reqVO);
                    deviceDO.setMac(reqVO.getMac());
                    deviceDO.setAreaCode(deviceService.getProvinceByCode(reqVO.getAreaCode()));
                    deviceDO.setManufacturer(reqVO.getManufacturer());
                    deviceDO.setFwVersion(reqVO.getFirmwareVersion());
                    deviceDO.setHdVersion(reqVO.getHardwareVersion());
                    boolean isApp = !FotaStrategyUpgradeTypeEnum.SILENT.getType().equals(reqVO.getCheckType());
                    if (mrgProperties.getNoSupportOtaProvinceSet().contains(deviceDO.getAreaCode())) {
                        log.info("设备{}所属省份{}已配置禁止升级", deviceDO.getSn(), deviceDO.getAreaCode());
                        return Mono.just(CommonResult.success(EMPTY));
                    }

                    DeviceCheckFotaRespDTO deviceCheckFotaRespDTO = fotaStrategyService.checkDeviceUpgrade(deviceDO, isApp);
                    Long cost = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - nanoStart);
                    log.info("recv device check-upgrade [sign={}] req=>{} resp=>{} cost:{}", sign, JSON.toJSONString(reqVO), JSON.toJSONString(deviceCheckFotaRespDTO), cost);
                    if (deviceCheckFotaRespDTO == null || deviceCheckFotaRespDTO.getUrl() == null) {
                        return Mono.just(CommonResult.success(EMPTY));
                    } else {
                        return Mono.just(CommonResult.success(deviceCheckFotaRespDTO));
                    }
                });
    }

    @Resource
    private FotaProgressRepository fotaProgressRepository;

    public void checkAndUpdateDevice(DeviceDO deviceDO, DeviceCheckUpgradeReqVO deviceOtaCheckDTO) {
        boolean hasChange = false;
        DeviceDO tmp = new DeviceDO();
        tmp.setId(deviceDO.getId());
        tmp.setSn(deviceDO.getSn());
        tmp.setProductKey(deviceDO.getProductKey());

        if (deviceOtaCheckDTO.getAreaCode() != null && !StringUtils.equals(deviceService.getProvinceByCode(deviceOtaCheckDTO.getAreaCode()), deviceDO.getAreaCode())) {
            hasChange = true;
            deviceDO.setAreaCode(deviceService.getProvinceByCode(deviceOtaCheckDTO.getAreaCode()));
            tmp.setAreaCode(deviceService.getProvinceByCode(deviceOtaCheckDTO.getAreaCode()));
        }
        if (!StringUtils.equals(deviceOtaCheckDTO.getMac(), deviceDO.getMac())) {
            hasChange = true;
            deviceDO.setMac(deviceOtaCheckDTO.getMac());
            tmp.setMac(deviceOtaCheckDTO.getMac());
        }
        if (!StringUtils.equals(deviceOtaCheckDTO.getFirmwareVersion(), deviceDO.getFwVersion())) {
            hasChange = true;
            fotaProgressRepository.findFirstByDeviceIdAndAndOldVersionOrderByCreateTimeDesc(deviceDO.getId(), deviceDO.getFwVersion())
                    .ifPresent(fotaProgressDO -> {
                        if (!FotaUpgradeStatusEnum.SUCCESS.getStatus().equals(fotaProgressDO.getStatus())) {
                            fotaProgressDO.setStatus(FotaUpgradeStatusEnum.SUCCESS.getStatus());
                            fotaProgressDO.setProgress(100);
                            fotaProgressDO.setUpdateTime(new Date());
                            log.info("修复设备升级记录{}  {} {}", deviceDO.getSn(), deviceDO.getFwVersion(), deviceOtaCheckDTO.getFirmwareVersion());
                            fotaProgressRepository.save(fotaProgressDO);
                        }
                        if (!strategyUpgradeRecRedisCache.checkDeviceHasUpgradedByStrategy(fotaProgressDO.getStrategyId(), deviceDO.getId())) {
                            StrategyUpgradeRecDO rec = new StrategyUpgradeRecDO();
                            rec.setStrategyId(fotaProgressDO.getStrategyId());
                            rec.setDeviceId(deviceDO.getId());
                            rec.setCreateTime(new Date());
                            rec.setId(YitIdHelper.nextId());
                            strategyUpgradeRecRepository.save(rec);
                            strategyUpgradeRecRedisCache.addDeviceToStrategyUpgradedRec(fotaProgressDO.getStrategyId(), deviceDO.getId());
                        }


                    });
            deviceDO.setFwVersion(deviceOtaCheckDTO.getFirmwareVersion());
            tmp.setFwVersion(deviceOtaCheckDTO.getFirmwareVersion());
        }
        if (!StringUtils.equals(deviceOtaCheckDTO.getHardwareVersion(), deviceDO.getHdVersion())) {
            tmp.setHdVersion(deviceOtaCheckDTO.getHardwareVersion());
            deviceDO.setHdVersion(deviceOtaCheckDTO.getHardwareVersion());
            hasChange = true;
        }
        if (!StringUtils.equals(deviceOtaCheckDTO.getManufacturer(), deviceDO.getManufacturer())) {
            hasChange = true;
            deviceDO.setManufacturer(deviceOtaCheckDTO.getManufacturer());
            tmp.setManufacturer(deviceOtaCheckDTO.getManufacturer());
        }
        if (hasChange) {
            log.info("check-update device info change {}", JSON.toJSONString(deviceDO));
            deviceService.update(tmp);
        }
    }

    @Resource
    private FotaProgressMapper fotaProgressMapper;
    @Resource
    private StrategyUpgradeRecRedisCache strategyUpgradeRecRedisCache;
    @Resource
    private StrategyUpgradeRecRepository strategyUpgradeRecRepository;
    @Resource
    private FirmwareMapper firmwareMapper;

    @PostMapping("/device-api/device/upgrade-progress")
    public Mono<CommonResult> upgradeProgress(@RequestHeader(value = "sign", required = true) String sign,
                                              @RequestHeader(value = "version", required = false) String version,
                                              @RequestHeader(value = "signMethod", required = false) String signMethod,
                                              @RequestBody DeviceUpgradeProgressReqVO reqVO) {
        log.info("recv device upgrade-progress [sign={},signMethod={},version={}] {}", sign, signMethod, version, JSON.toJSONString(reqVO));

        DeviceDO deviceDO = deviceService.find(reqVO.getProductKey(), reqVO.getSn());
        if (deviceDO == null || deviceDO.getId() == null) {
            return Mono.just(CommonResult.error(DEVICE_NOT_EXISTS));
        }
        String valueToDigest = reqVO.getSn() + "_" + reqVO.getProductKey() + "_" + reqVO.getTimestamp();
        String encrySign = EncryptUtil.hmacSha1(valueToDigest, deviceDO.getSecret());
        if (!StringUtils.equals(encrySign, sign)) {
            log.info("device {} check-upgrade fail,sign {} not eq {}", reqVO.getSn(), sign, encrySign);
            return Mono.just(CommonResult.error(UNAUTHORIZED));
        }

        FotaStrategyDO fotaStrategyDO = fotaStrategyService.getStrategy(Long.parseLong(reqVO.getStrategy()));
        FotaProgressDO fotaProgressDO = fotaProgressMapper.findProgressByStrategyAndDeviceId(Long.parseLong(reqVO.getStrategy()), deviceDO.getId());
        if (fotaProgressDO == null) {
            fotaProgressDO = new FotaProgressDO();
            fotaProgressDO.setId(YitIdHelper.nextId());
            fotaProgressDO.setFirmwareId(fotaStrategyDO.getFirmwareId());
            if (fotaProgressDO != null) {
                fotaProgressDO.setFirmwareId(fotaStrategyDO.getFirmwareId());
            }
            fotaProgressDO.setStrategyId(fotaStrategyDO.getId());
            fotaProgressDO.setSn(deviceDO.getSn());
            fotaProgressDO.setOldVersion(reqVO.getOldVersion());
            fotaProgressDO.setDeviceId(deviceDO.getId());
            fotaProgressDO.setProductId(deviceDO.getProductId());
            fotaProgressDO.setCreateTime(new Date());
            fotaProgressDO.setStatus(FotaUpgradeStatusEnum.WAIT_UPGRADE.getStatus());
        }
        fotaProgressDO.setProgress(reqVO.getProgress());
        fotaProgressDO.setMsg(reqVO.getReason());
        if (reqVO.getProgress() < 0) {
            fotaProgressDO.setStatus(FotaUpgradeStatusEnum.FAILURE.getStatus());
        } else if (reqVO.getProgress() == 0) {
            fotaProgressDO.setStatus(FotaUpgradeStatusEnum.WAIT_UPGRADE.getStatus());
        } else if (reqVO.getProgress() < PROGRESS_100) {
            fotaProgressDO.setStatus(FotaUpgradeStatusEnum.UPGRADING.getStatus());
        } else {
            fotaProgressDO.setMsg("");
            fotaProgressDO.setStatus(FotaUpgradeStatusEnum.SUCCESS.getStatus());
            if (fotaStrategyDO != null) {
                if (!strategyUpgradeRecRedisCache.checkDeviceHasUpgradedByStrategy(fotaStrategyDO.getId(), deviceDO.getId())) {
                    StrategyUpgradeRecDO rec = new StrategyUpgradeRecDO();
                    rec.setStrategyId(fotaStrategyDO.getId());
                    rec.setDeviceId(deviceDO.getId());
                    rec.setCreateTime(new Date());
                    rec.setId(YitIdHelper.nextId());
                    strategyUpgradeRecRedisCache.addDeviceToStrategyUpgradedRec(fotaStrategyDO.getId(), deviceDO.getId());
                    strategyUpgradeRecRepository.save(rec);
                }
                FirmwareDO firmwareDO = fotaStrategyService.getFirmware(fotaStrategyDO.getFirmwareId());
                if (Objects.equals(firmwareDO.getStatus(), FirmwareStatusEnum.UN_VALIDATE.getStatus())) {
                    //未验证状态需要更新为已验证
                    firmwareDO.setStatus(FirmwareStatusEnum.VALIDATED.getStatus());
                    firmwareMapper.updateById(firmwareDO);
                }
            }
        }
        fotaProgressDO.setUpdateTime(new Date());
        if (StringUtils.isNoneBlank(deviceDO.getOwner())) {
            ThingModelSendMsgDTO<DeviceOtaProgressDTO> deviceOtaProgressMsg = new ThingModelSendMsgDTO<>();
            deviceOtaProgressMsg.setId(YitIdHelper.nextId());
            deviceOtaProgressMsg.setMethod(MsgMethodEnums.DEVICE_OTA_PROGRESS.getMethod());
            deviceOtaProgressMsg.setTimestamp(System.currentTimeMillis());
            deviceOtaProgressMsg.setVersion("2.0");
            DeviceOtaProgressDTO deviceOtaProgressDTO = new DeviceOtaProgressDTO();
            deviceOtaProgressDTO.setStep(reqVO.getProgress());
            deviceOtaProgressDTO.setStrategy(reqVO.getStrategy());
            deviceOtaProgressDTO.setCurVersion(reqVO.getOldVersion());
            deviceOtaProgressMsg.setParams(deviceOtaProgressDTO);
            String snedToApp = JSON.toJSONString(deviceOtaProgressMsg);
            log.info("send ota msg to app {} {} {}", deviceDO.getSn(), deviceDO.getOwner(), snedToApp);
            mqttMsgUtil.sendToApp(Long.parseLong(deviceDO.getOwner()), snedToApp);
        }
        try {
            fotaProgressMapper.save(fotaProgressDO);
        } catch (Exception e) {
            log.error("save fotaProgressDO error", e);
        }

        return Mono.just(CommonResult.success(true));
    }


    @Resource
    private MqttMsgUtil mqttMsgUtil;

    @PostMapping("/device-api/device/factory-verification")
    public Mono<CommonResult> factoryVerification(@RequestBody @Validated List<DeviceFactoryVerificationReqVO> reqVOList) {
        log.info("设备出厂验证{}", JSON.toJSONString(reqVOList));
        if (reqVOList.size() > FACTORY_VERIFICATION_MAX_COUNT) {
            return Mono.just(CommonResult.error(606, "一次请求验证设备数量超过100"));
        }

        for (DeviceFactoryVerificationReqVO reqVO : reqVOList) {
            String productKey = reqVO.getProductKey();
            String sn = reqVO.getSn();
            ProductDO productDO = productService.find(productKey);
            if (productDO == null) {
                return Mono.just(CommonResult.error(601, String.format("SN:%s产品编码不存在", reqVO.getSn())));
            }

            if (!Objects.equals(EncryptUtil.md5(productDO.getSecret()).toLowerCase(), reqVO.getProductSecret().toLowerCase())) {
                return Mono.just(CommonResult.error(602, String.format("SN:%s产品密钥错误", reqVO.getSn())));
            }

            DeviceDO deviceDO = deviceService.find(productKey, sn);
            if (deviceDO == null) {
                return Mono.just(CommonResult.error(603, String.format("SN:%s的设备不存在", reqVO.getSn())));
            }
            if (!Objects.equals(EncryptUtil.md5(deviceDO.getSecret()).toLowerCase(), reqVO.getDeviceSecret().toLowerCase())) {
                return Mono.just(CommonResult.error(602, String.format("SN:%s设备密钥错误", reqVO.getSn())));
            }

            if (reqVO.getMac() != null) {
                if (!Objects.equals(deviceDO.getMac(), reqVO.getMac())) {
                    return Mono.just(CommonResult.error(602, String.format("SN:%s设备MAC和导入的不一致", reqVO.getSn())));
                }
            }
        }
        return Mono.just(CommonResult.success(true));
    }


    @Resource
    private MrgProperties mrgProperties;
    private static final String ALL = "_all";

    @PostMapping("/device-api/device/get-settings")
    public Mono<CommonResult> getSettings(ServerHttpRequest request, @RequestHeader(value = "sign", required = true) String sign,
                                          @RequestHeader(value = "version", required = false) String version,
                                          @RequestHeader(value = "signMethod", required = false) String signMethod,
                                          @RequestBody DeviceGetSettingReqVO reqVO) {
        Long nanoStart = System.nanoTime();
        String ip = IpUtil.getIPAddress(request);
        log.info("recv device get-settings [sign={},signMethod={}],version={}] {}", sign, signMethod, version, JSON.toJSONString(reqVO));
        Mono<DeviceDO> deviceMono = deviceService.findReactive(reqVO.getProductKey(), reqVO.getSn());
        return deviceMono.defaultIfEmpty(new DeviceDO()).flatMap(deviceDO -> {
            if (deviceDO.getId() == null) {
                return Mono.just(CommonResult.error(DEVICE_NOT_EXISTS));
            }
            if (mrgProperties.getCheckSign()) {
                String valueToDigest = reqVO.getSn() + "_" + reqVO.getProductKey() + "_" + reqVO.getTimestamp();
                String encrySign = EncryptUtil.hmacSha1(valueToDigest, deviceDO.getSecret());
                if (!StringUtils.equals(encrySign, sign)) {
                    log.info("device {} get-settings fail,sign {} not eq {}", reqVO.getSn(), sign, encrySign);
                    return Mono.just(CommonResult.error(UNAUTHORIZED));
                }
            }
            DeviceSettingsRespVO deviceSettingsRespVO = new DeviceSettingsRespVO();
            deviceSettingsRespVO.setMinInterval(deviceRetryStrategyProperties.getMinInterval());
            deviceSettingsRespVO.setMaxInterval(deviceRetryStrategyProperties.getMaxInterval());
            int fotaCheckInterval = deviceRetryStrategyProperties.getFotaCheckInterval();
            deviceSettingsRespVO.setFotaCheckInterval(fotaCheckInterval + ThreadLocalRandom.current().nextInt(fotaCheckInterval / 10));
            if (!Objects.equals(deviceDO.getIp(), ip)) {
                log.info("device {} ip change from {} to {}", deviceDO.getSn(), deviceDO.getIp(), ip);
                deviceDO.setIp(ip);
                DeviceDO tmp = new DeviceDO();
                tmp.setId(deviceDO.getId());
                tmp.setProductKey(deviceDO.getProductKey());
                tmp.setSn(deviceDO.getSn());
                tmp.setIp(ip);
                deviceService.updateOnline(tmp);
            }
            ProductDO productDO = productService.find(deviceDO.getProductKey());
            long cost = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - nanoStart);
            log.info("no-hcdn device {} get-settings resp {} cost {}", deviceDO.getSn(), JSON.toJSONString(deviceSettingsRespVO), cost);
            deviceSettingsRespVO.setOpenSpeedUp(false);
            deviceSettingsRespVO.setForceSpeedUp(false);
            return Mono.just(CommonResult.success(deviceSettingsRespVO));
        });
    }


    @Resource
    private ReactiveDeviceRepository reactiveDeviceRepository;

    @PostMapping("/device-api/device/get-info-batch")
    public Mono<CommonResult> getDeviceInfoBatch(@RequestHeader(value = HttpHeaders.AUTHORIZATION, required = true) String token, @RequestBody Set<String> snList) {
        log.info("get-info-batch by snList {}", JSON.toJSONString(snList));
        if (!mrgProperties.getInnerToken().equals(token)) {
            return Mono.just(CommonResult.error(UNAUTHORIZED.getCode(), "鉴权失败"));
        }
        Set<String> snSet = new HashSet<>(snList.size());

        return reactiveDeviceRepository.findAllBySnIn(snList)
                .map(deviceDO -> new DeviceInfoDTO(deviceDO.getSn(), deviceDO.getActivateTime(), deviceDO.getOnlineTime(), deviceDO.getFwVersion()))
                .filter(deviceInfoDTO -> {
                    if (snSet.contains(deviceInfoDTO.getSn())) {
                        return false;
                    } else {
                        snSet.add(deviceInfoDTO.getSn());
                        return true;
                    }

                })
                .collectList()
                .map(deviceInfoDTOS -> CommonResult.success(deviceInfoDTOS));
    }


    private String getMqttConnectionUrl(DeviceDO deviceDO) {
        MqttNodeDO mqttNodeDO;
        if (deviceDO.getNodeId() != null && deviceDO.getNodeId() != 0) {
            mqttNodeDO = mqttNodeService.findByIdFromCache(deviceDO.getNodeId());
            if (mqttNodeDO == null || !Objects.equals(mqttNodeDO.getStatus(), MqttNodeStatus.ONLINE.getStatus())) {
                mqttNodeDO = mqttNodeService.getRandomHealthNode();
            }
        } else {
            mqttNodeDO = mqttNodeService.getRandomHealthNode();
        }
        if (mqttNodeDO == null) {
            return null;
        }
        if (deviceDO.getConfigs() != null && deviceDO.getConfigs().get(MQTT_URL_CONFIG_PROP) != null) {
            return (String) deviceDO.getConfigs().get(MQTT_URL_CONFIG_PROP);
        }
        String url = "mqtt://" + mqttNodeDO.getExternalHost() + ":" + mqttNodeDO.getMqttPort();
        return url;
    }

    private String getSSLMqttConnectionUrl(DeviceDO deviceDO) {
        MqttNodeDO mqttNodeDO;
        if (deviceDO.getNodeId() != null && deviceDO.getNodeId() != 0) {
            mqttNodeDO = mqttNodeService.findByIdFromCache(deviceDO.getNodeId());
            if (mqttNodeDO == null || !Objects.equals(mqttNodeDO.getStatus(), MqttNodeStatus.ONLINE.getStatus())) {
                mqttNodeDO = mqttNodeService.getRandomHealthNode();
            }
        } else {
            mqttNodeDO = mqttNodeService.getRandomHealthNode();
        }
        String url = "ssl://" + mqttNodeDO.getExternalHost() + ":" + 8883;
        return url;
    }

    public static void main(String[] args) {
        long ts = **********;
        String sign = EncryptUtil.hmacSha1("081115010002255_vHrCdaNNOW_**********", "qh65h6ufpxb6gr7d");
        System.out.println(sign);
        Long a=12312312321L;
        Long b=12312312321L;
        System.out.println(a==b);

    }
}
