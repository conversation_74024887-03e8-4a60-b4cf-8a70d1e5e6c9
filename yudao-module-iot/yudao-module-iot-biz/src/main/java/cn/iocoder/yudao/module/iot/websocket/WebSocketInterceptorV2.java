package cn.iocoder.yudao.module.iot.websocket;

import cn.iocoder.yudao.module.iot.config.BaiduAsrProperties;
import cn.iocoder.yudao.module.iot.util.SignUtil;
import cn.iocoder.yudao.module.system.config.AiProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.server.HandshakeInterceptor;

import javax.annotation.Resource;
import java.net.URI;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class WebSocketInterceptorV2 implements HandshakeInterceptor {
    @Resource
    private SignUtil signUtil;
    @Resource
    private AiProperties aiProperties;
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                 WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        log.info("经过websocket拦截器");
        if (request instanceof ServletServerHttpRequest) {
            ServletServerHttpRequest servletRequest = (ServletServerHttpRequest) request;
            // 获取token参数
            String token = servletRequest.getServletRequest().getParameter("sign");
            String deviceNo = servletRequest.getServletRequest().getParameter("deviceNo");
            String productKey = servletRequest.getServletRequest().getParameter("productKey");
            String productId = servletRequest.getServletRequest().getParameter("productId");
            String deviceId = servletRequest.getServletRequest().getParameter("deviceId");
            String sn = servletRequest.getServletRequest().getParameter("sn");
            String fromLang = servletRequest.getServletRequest().getParameter("fromLang");
            String ts= servletRequest.getServletRequest().getParameter("ts");
            String toLang= servletRequest.getServletRequest().getParameter("toLang");

            attributes.put("toLang",toLang);
            attributes.put("fromLang",fromLang);
            log.info("asr websocket deviceId={} sign={}",deviceId,token);
            if(StringUtils.isAnyBlank(ts,deviceId,token)){
                log.info("缺少鉴权参数，断开连接 {} {} {}",ts,deviceId,token);
                return false;
            }

            if(aiProperties.getAuthCheck() ){
                boolean checkSign = signUtil.checkSign(deviceId,Long.parseLong(ts),token);
                if(!checkSign){
                    log.error("ASRChatV1认证失败{} {} {}",deviceId,ts,token);
                    return false;
                }
            }
            // 将token存储在属性中，供后续使用
            attributes.put("sign", token);
            attributes.put("sn",sn);
            attributes.put("deviceNo",deviceNo);
            attributes.put("productKey",productKey);
            attributes.put("productId",productId);
            attributes.put("deviceId",deviceId);
            AsrWebSocketHandlerV2.AsrWebSocketClient asrClient = createAsrClient(null, sn);
            attributes.put("asrClient",asrClient);
            connectWithTimeout(asrClient);
            return true;
        }
        return false;
    }
    private static final int CONNECTION_TIMEOUT_SECONDS = 5;
    private static final int RETRY_INTERVAL_MS = 1;
    private void connectWithTimeout(AsrWebSocketHandlerV2.AsrWebSocketClient asrClient) throws InterruptedException {
        asrClient.connect();
        long startTime = System.currentTimeMillis();
        long timeout = TimeUnit.SECONDS.toMillis(CONNECTION_TIMEOUT_SECONDS);

        while (!asrClient.isOpen()) {
            if (System.currentTimeMillis() - startTime > timeout) {
                log.error("ASR WebSocket connection timeout {}",asrClient.getClientSession().getId());
                throw new RuntimeException("ASR WebSocket connection timeout");
            }
            Thread.sleep(RETRY_INTERVAL_MS);
        }
    }

    @Resource
    private BaiduAsrProperties baiduAsrProperties;
    private String buildWsUrl(String sn) {
        return String.format("%s?sn=%s", baiduAsrProperties.getServerUrl(), sn);
    }
    public AsrWebSocketHandlerV2.AsrWebSocketClient createAsrClient(WebSocketSession clientSession, String sn) {
        try {
            String wsUrl = buildWsUrl(sn);
            AsrWebSocketHandlerV2.AsrWebSocketClient asrWebSocketClient= new AsrWebSocketHandlerV2.AsrWebSocketClient(new URI(wsUrl), clientSession, sn);
            return asrWebSocketClient;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create ASR WebSocket client", e);
        }
    }
    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                             WebSocketHandler wsHandler, Exception exception) {
    }

    private boolean validateToken(String token) {
        // 实现你的token验证逻辑
        return true; // 这里需要替换为实际的token验证
    }
}