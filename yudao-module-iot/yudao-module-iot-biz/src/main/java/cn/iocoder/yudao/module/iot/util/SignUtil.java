package cn.iocoder.yudao.module.iot.util;


import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.util.encrypt.EncryptUtil;
import cn.iocoder.yudao.module.iot.dal.dataobject.device.DeviceDO;
import cn.iocoder.yudao.module.iot.dal.mongo.DeviceMapper;
import cn.iocoder.yudao.module.iot.service.device.DeviceService;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class SignUtil {
    @Resource
    private DeviceMapper deviceMapper;
    private static final Cache<Long, DeviceDO> deviceCache = CacheBuilder.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000) // 设置缓存的最大容量，防止内存占用过多
            .build();

    public boolean checkSign(String deviceId, Long ts, String sign) {
        if (!NumberUtil.isNumber(deviceId) ||StringUtils.isBlank(deviceId)) {
            return false;
        }

        // 从缓存中获取设备信息
        Long deviceIdLong = Long.parseLong(deviceId);
        DeviceDO deviceDO = deviceCache.getIfPresent(deviceIdLong);

        // 如果缓存中没有，则从数据库中查询，并放入缓存
        if (deviceDO == null) {
            deviceDO = deviceMapper.selectById(deviceIdLong);
            if (deviceDO != null) {
                deviceCache.put(deviceIdLong, deviceDO);
            }
        }

        // 如果设备信息仍然为空，返回false
        if (deviceDO == null) {
            log.info("chekcSign failed, cannot find device by id: {}", deviceId);
            return false;
        }

        // 计算签名并校验
        String computeSign = EncryptUtil.md5(deviceDO.getSecret() + ts);
        if(computeSign.equals(sign)){
            return true;
        }else{
            log.info( "checkSign failed, computeSign: {}, sign: {}", computeSign, sign);
            return false;
        }
    }
}
