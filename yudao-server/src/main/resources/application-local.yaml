server:
  port: 48080

one-api-proxy:
  api:
    servlet_url: /admin-api/one-api/*
    target_url: https://one-api.starrycfn.com:30101
    log_enabled: true
    token: 0f76593d78ca439c8a6bc0bd9ac0e444

yunpan:
  config:
    xunfei:
      app_id: 1917145796315320320
      app_key: 5cabc55ae9414dd1569a3855f6f521d9
      secret_key: c72cc1283a5703e787a127610c0456023103af73346fd69f32edc4db4fd1194e
      product_id: 1879832043032350721
      allow_url:
        - "/open-mpplatform/oauth2/refreshToken"
        - "/open-mpplatform/oauth2/accessToken1"
        - "/richlifeApp/devapp/pcUploadFileRequest"
        - "/richlifeApp/devapp/downloadRequest"
        - "/richlifeApp/devapp/getUserInfo"
        - "/richlifeApp/devapp/getDiskInfo"
        - "/richlifeApp/devapp/getUserAccountBrief"
        - "/richlifeApp/devapp/updateContentInfo"
        - "/richlifeApp/devapp/createCatalogExt"
        - "/richlifeApp/devapp/getAppFileId"
        - "/richlifeApp/devapp/getdisk"
    test:
      app_id: test1867156481711935488
      app_key: d3bc4943152af407c869039c4840b6d3
      secret_key: 71b6c50b0d054cbb639303bc77f62457b796d9190f83c83d1741c4593cf0657e
      product_id: 1879832043032350721
  open-mpplatform:
    servlet_url: /yunpan/open-mpplatform/*
    target_url: https://miniapp.yun.139.com/open-mpplatform
    log_enabled: true
    forward_ip: false
  richlife-app:
    servlet_url: /yunpan/richlifeApp/*
    target_url: https://miniapp.yun.139.com/richlifeApp
    log_enabled: true
    forward_ip: false
  middle:
    servlet_url: /yunpan/middle/*
    target_url: https://miniapp.yun.139.com/middle
    log_enabled: true
    forward_ip: false
    redirect_url: https://ivs.chinamobiledevice.com:11443/yunpan/success/5986231b690d4dbc806f3ec0256fe160


--- #################### 数据库相关配置 ####################
#yudao:
#  info:
#    version: 2.0.0
#    base-package: cn.iocoder.yudao
tengxunyun:
  secretId: AKIDlPuPhXZNvEKBnihU5bsteARSEm6C3f2A
  secretKey: xfCXfbJn1zg8KHwOdjleIhsvmQEyXsxA
  region: ap-guangzhou
spring:
  # 数据源配置项
  servlet:
    multipart:
      max-file-size: "300MB"
      max-request-size: "300MB"
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        #        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          name: niot
          url: *********************************/${spring.datasource.dynamic.datasource.master.name}?allowMultiQueries=true&useUnicode=true&useSSL=false&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
          # name: yudao
          # url: ********************************/${spring.datasource.dynamic.datasource.master.name}?allowMultiQueries=true&useUnicode=true&useSSL=false&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
          username: root
          password: hGgHi0WgBBWwedcWxWpS
        tdengine:
          name: tdengine
          dialect: mysql
          url: jdbc:TAOS-RS://*************:6041/niot?timezone=Asia/Beijing&charset=utf-8
          username: root
          password: taosdata
          driver-class-name: com.taosdata.jdbc.rs.RestfulDriver
          connection-test-query: "SELECT 1"
          enabled: true
  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    host: ************** # 地址
    port: 26379 # 端口
    database: 0 # 数据库索引
    password: F5aq6XyVMSLR6z7G0Ses
    connect-timeout: 5000
    timeout: 5000
  data:
    mongodb:
      uri: "*******************************************************************************************"
#      username:
#      password:
#      replica-set-name: rs0

jasypt:
  encryptor:
    password: yuanma # 加解密的秘钥
    bootstrap: false
--- #################### 定时任务相关配置 ####################

# Quartz 配置项，对应 QuartzProperties 配置类
spring:
  quartz:
    auto-startup: true # 本地开发环境，尽量不要开启 Job
    scheduler-name: schedulerName # Scheduler 名字。默认为 schedulerName
    job-store-type: jdbc # Job 存储器类型。默认为 memory 表示内存，可选 jdbc 使用数据库。
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    properties: # 添加 Quartz Scheduler 附加属性，更多可以看 http://www.quartz-scheduler.org/documentation/2.4.0-SNAPSHOT/configuration.html 文档
      org:
        quartz:
          # Scheduler 相关配置
          scheduler:
            instanceName: schedulerName
            instanceId: AUTO # 自动生成 instance ID
          # JobStore 相关配置
          jobStore:
            # JobStore 实现类。可见博客：https://blog.csdn.net/weixin_42458219/article/details/122247162
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            isClustered: true # 是集群模式
            clusterCheckinInterval: 15000 # 集群检查频率，单位：毫秒。默认为 15000，即 15 秒
            misfireThreshold: 60000 # misfire 阀值，单位：毫秒。
          # 线程池相关配置m
          threadPool:
            threadCount: 25 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级
            class: org.quartz.simpl.SimpleThreadPool # 线程池类型
    jdbc: # 使用 JDBC 的 JobStore 的时候，JDBC 的配置
      initialize-schema: NEVER # 是否自动使用 SQL 初始化 Quartz 表结构。这里设置成 never ，我们手动创建表结构。

--- #################### 配置中心相关配置 ####################

# Apollo 配置中心
apollo:
  bootstrap:
    enabled: true # 设置 Apollo 在启动阶段生效
    eagerLoad:
      enabled: true # 设置 Apollo 在日志初始化前生效，可以实现日志的动态级别配置
  jdbc: # 自定义的 JDBC 配置项，用于数据库的地址
    dao: cn.iocoder.yudao.module.infra.dal.mysql.config.ConfigDAOImpl
    url: ${spring.datasource.dynamic.datasource.master.url}
    username: ${spring.datasource.dynamic.datasource.master.username}
    password: ${spring.datasource.dynamic.datasource.master.password}

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

# Resilience4j 配置项
resilience4j:
  ratelimiter:
    instances:
      backendA:
        limit-for-period: 1 # 每个周期内，允许的请求数。默认为 50
        limit-refresh-period: 60s # 每个周期的时长，单位：微秒。默认为 500
        timeout-duration: 1s # 被限流时，阻塞等待的时长，单位：微秒。默认为 5s
        register-health-indicator: true # 是否注册到健康监测

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        url: http://127.0.0.1:${server.port}/${spring.boot.admin.context-path} # 设置 Spring Boot Admin Server 地址
        instance:
          prefer-ip: true # 注册实例时，优先使用 IP
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Sprin

# 日志文件配置
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    cn.iocoder.yudao.module.bpm.dal.mysql: debug
    cn.iocoder.yudao.module.infra.dal.mysql: debug
    cn.iocoder.yudao.module.pay.dal.mysql: debug
    cn.iocoder.yudao.module.system.dal.mysql: debug
    cn.iocoder.yudao.module.tool.dal.mysql: debug
    cn.iocoder.yudao.module.member.dal.mysql: debug
    com.baomidou.mybatisplus.core.executor.BaseExecutor: debug
    com.baomidou.mybatisplus.core.mapper.BaseMapper: debug

debug:
  configuration:
    # 指定使用Log4j2作为日志实现
    logger:
      org:
        apache:
          logging:
            log4j:
              core:
                AsyncLogger:
                  - name: com.baomidou.mybatisplus.core.executor.BaseExecutor
                    level: debug
--- #################### 微信公众号相关配置 ####################
wx: # 参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-mp-spring-boot-starter/README.md 文档
  mp:
    # 公众号配置(必填)
    app-id: wx041349c6f39b268b
    secret: 5abee519483bc9f8cb37ce280e814bd0
    # 存储配置，解决 AccessToken 的跨节点的共享
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wx # Redis Key 的前缀 TODO 芋艿：解决下 Redis key 管理的配置
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台

--- #################### 芋道相关配置 ####################

# 芋道配置项，设置当前项目所有自定义的配置
yudao:
  captcha:
    enable: false # 本地环境，暂时关闭图片验证码，方便登录等接口的测试
  security:
    mock-enable: true
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  pay:
    pay-notify-url: http://niubi.natapp1.cc/api/pay/order/notify
    pay-return-url: http://niubi.natapp1.cc/api/pay/order/return
    refund-notify-url: http://niubi.natapp1.cc/api/pay/refund/notify
  access-log: # 访问日志的配置项
    enable: false
  error-code: # 错误码相关配置项
    enable: false
  demo: false # 关闭演示模式
  swagger:
    title: 管理后台
    description: 提供管理员管理的所有功能
    version: ${yudao.info.version}
    base-package: ${yudao.info.base-package}
    enable: "true"

justauth:
  enabled: true
  type:
    DINGTALK: # 钉钉
      client-id: dingvrnreaje3yqvzhxg
      client-secret: i8E6iZyDvZj51JIb0tYsYfVQYOks9Cq1lgryEjFRqC79P3iJcrxEwT6Qk2QvLrLI
      ignore-check-redirect-uri: true
    WECHAT_ENTERPRISE: # 企业微信
      client-id: wwd411c69a39ad2e54
      client-secret: 1wTb7hYxnpT2TUbIeHGXGo7T0odav1ic10mLdyyATOw
      agent-id: 1000004
      ignore-check-redirect-uri: true
  cache:
    type: REDIS
    prefix: 'social_auth_state:' # 缓存前缀，目前只对 Redis 缓存生效，默认 JUSTAUTH::STATE::
    timeout: 24h # 超时时长，目前只对 Redis 缓存生效，默认 3 分钟


cmi:
  auth:
    sourceId: "020085"
    sourceKey: "um33Zyc8X8989bse"
    authUrl: https://token.cmpassport.com:8300/uniapi/uniTokenValidate

pagehelper:
  helperDialect: postgresql

iot:
  auth:
    pubkey: "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChnxgwtz1pK44PDZsbtrS0df+x5ELG2Bd0+UzUqCO8DKkkgPRNrofl14kGsPVilY1lfbgQEqUdl2yrAlUcD2SOyJOXmm+YSX+rofVmAhHqpSv04t6h217xwQQeojHac3kwdgFmH2O67r/x/Elyz1GI5b7gqZ74zxFg4iiY5YxThQIDAQAB"
    prikey: "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKGfGDC3PWkrjg8Nmxu2tLR1/7HkQsbYF3T5TNSoI7wMqSSA9E2uh+XXiQaw9WKVjWV9uBASpR2XbKsCVRwPZI7Ik5eab5hJf6uh9WYCEeqlK/Ti3qHbXvHBBB6iMdpzeTB2AWYfY7ruv/H8SXLPUYjlvuCpnvjPEWDiKJjljFOFAgMBAAECgYAQrkbk4ESqUiB7VUApABZuea9GQPTTaQIixIg1epUVl4jth5GvYtAL3M77rc/luOG7E9qnbgU0pKhGjhsSplQObm4M9SfGmb4SjAKebRrSMxj/pwcjDEDla8UkEYc7qGN6Lc8rEIXQeDQrfG2yVTfS+tvqNS8258xiTFY77RUzkQJBANxOrHr9ebJx4EbWmvx2vyvi6P52a5zGnjOTJL9cZMcGqUI8B/Bqxx51OUpJ1MWxDOhD8Hcnvu6CFmhPYqPq9vMCQQC7zmdK3esPYhuDf0mYGqqRiby24nTKofOylhKc6xBBwBgIkuwMHpxiqVoN2OiP+rSCI7KKN8pXH/Cssf33GpmnAkBqUCT37R4vOLjsNl/weTVj7kpVkbv4mfh6FwXiuql/Sf2gEuwZ3s1cK2GMMfjVtczuwOpn9ewzmN9LUxPc2mR1AkAu8q8RoR5EzzKvkZ9VHb1r+nGpijzF9uxTxX5Lqdt7hEi9w/SsJu7THyC6+3FPpM2BBul38fBsDTj+AtTI8+ZbAkAL2l0lhQCpLN/x7PP+O/pINRMXfsIoL8tMwuKoXRn/njrZRe1Eqzd7L6VyTCenn6K48rbrcGVH2w9Gf20AdjBC"
  mrg:
    fota-check-url: "http://*************:48081/inner/fota/check-device-upgrade"
    device-clear-url: "http://*************:48081:48081/inner/device/update-cache"
  member-user:
    login-failed-limit: 10
    login-failed-frequency: 10m
    sms-send-limit: 3
    public-key: "MIIGcDCCBVigAwIBAgIQBRh0wuWs4S7FFEqggVJcyzANBgkqhkiG9w0BAQsFADBMMQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMSYwJAYDVQQDEx1EaWdpQ2VydCBTZWN1cmUgU2l0ZSBDTiBDQSBHMzAeFw0yMjA5MDgwMDAwMDBaFw0yMzEwMDkyMzU5NTlaMG8xCzAJBgNVBAYTAkNOMRIwEAYDVQQIDAnljJfkuqzluIIxMzAxBgNVBAoMKuS4reWbveenu+WKqOmAmuS/oembhuWboue7iOerr+aciemZkOWFrOWPuDEXMBUGA1UEAwwOKi5jeHp4MTAwODYuY24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCqg1gX7HG578vPxRyd3/eFhw6vRCtrTCv/317c3jxH9NjEeyHrVNJig6gyTbSSCXchh51x4OluhKyF0ZVAae0FKrV/4y7lUahSpMDbi6TitdZ73ZVba3MLWoFTt1VR8cJTvv9wHocfp/RwDX4v+h+T5tCMk5Q/7hameoSDXPsmeI36/z8ajfBUjTq0j63ErwMFGfgfU3XXTEsig+LYOIdsGjoGlCgtjGqLUt+qMNtqEisdI+Mrx/3k7ogBIu25TBND4WZGLBm+Nci6/byuni3rBOFaGgGQItXkvrY9sm0VQ0cv/rm3rwX8l1l32qAFBAKGjzvRNtT43VRR4NQwWxffAgMBAAGjggMpMIIDJTAfBgNVHSMEGDAWgBRE2chKM47TUo2nkpRhH5rIpbfsyzAdBgNVHQ4EFgQUOKdCBZiOnhiu1EI+ILCOYQp2yMYwJwYDVR0RBCAwHoIOKi5jeHp4MTAwODYuY26CDGN4engxMDA4Ni5jbjAOBgNVHQ8BAf8EBAMCBaAwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMEQGA1UdHwQ9MDswOaA3oDWGM2h0dHA6Ly9jcmwuZGlnaWNlcnQuY24vRGlnaUNlcnRTZWN1cmVTaXRlQ05DQUczLmNybDA+BgNVHSAENzA1MDMGBmeBDAECAjApMCcGCCsGAQUFBwIBFhtodHRwOi8vd3d3LmRpZ2ljZXJ0LmNvbS9DUFMweAYIKwYBBQUHAQEEbDBqMCMGCCsGAQUFBzABhhdodHRwOi8vb2NzcC5kaWdpY2VydC5jbjBDBggrBgEFBQcwAoY3aHR0cDovL2NhY2VydHMuZGlnaWNlcnQuY24vRGlnaUNlcnRTZWN1cmVTaXRlQ05DQUczLmNydDAJBgNVHRMEAjAAMIIBfgYKKwYBBAHWeQIEAgSCAW4EggFqAWgAdgDoPtDaPvUGNTLnVyi8iWvJA9PL0RFr7Otp4Xd9bQa9bgAAAYMd2oBEAAAEAwBHMEUCIGZRGdwEus+sUDQ2sAPsY7H2uqrHLxcOICgYF1OVvYLEAiEAp/O39vqX6a/r7QIbjcZLALeJmt46TtUeCtmzwMblVbEAdgA1zxkbv7FsV78PrUxtQsu7ticgJlHqP+Eq76gDwzvWTAAAAYMd2oCBAAAEAwBHMEUCIC+ynPm9QV0gN5L9e82DrREghW8SxVoBc+53y0oFWPJPAiEAidAOOLNCKUop7cqx5cy9Eyf7fS9gIHospnXLTxMUdisAdgCzc3cH4YRQ+GOG1gWp3BEJSnktsWcMC4fc8AMOeTalmgAAAYMd2oDFAAAEAwBHMEUCIQCpj1KhZtdf5jc6flI8klESoZqRxm8zc9Y5pLM59WyB6gIgCpp5QD5s5695K0UORJpfljAqUloRRmgxDBGgeYkYHoowDQYJKoZIhvcNAQELBQADggEBAJoNWLk+bfj4MjvqomVr1R3OBGKm1YAOLNnalmnKNf0B6qnQDat0p6J3UWplHl3yyZvTUwU4pUKo7J6yVXz+TEVa1FNyMFrAmm/JQWJsXliFVDcZ5yAdlPJeIlV5Sf5xuSHwMTslW2xwj2+Py5U/qTMHL3H9z8BHnEg5R0u6sYIsXW0SrASeZKCEcqfJ6qSRL5F1uO9UTn759LIWgnUsi/GTM548YzerZEgGw9+CKB+fhPvUBeicEJwYpzf5sGKnAliqEyRIhq/fbuPiWW574S+AFUtN01DF3SiUQS3dT4xdfeD03uzaM9AMwB3534150YCpwEABnw5xUdFHTAF50+M="
    key-sha256: "+P4PoxqVpVxrOBIH4asF+zJvUZPl/h3Ai2OF402PjfM="
  address: "http://localhost:38081/"
  emq-alarm:
    receivers:
      - <EMAIL>
      - <EMAIL>
  du:
    host: https://xiaodu.baidu.com
    cloud-id: cmcc-hcdn
    cloud-id-sercet: 5D98F5995E7343DEC869E0642BBC1A8F
    client-ids: '{1:"eTRMzmYKxR2cj1l79ftWfBQyGuLzWPWT",0:"jTxnug8GPrW4AdEIlGVc7zaqi2Grs4Go"}'

  mqtt:
    api:
      appid: admin
      secret: public
  max-monitor-speed:
    url: https://opentuna.cn/ubuntu-releases/bionic/ubuntu-18.04.6-live-server-amd64.iso
    time: 5
    interval: 100
  limit-set-property-frequency:
    identifiers:
      - networkInfo
    frequency: 30s
  device-share:
    expire-minutes: 5
    length: 6
    user-limit: 20
    app-download-url: https://h5.cxzx10086.cn/spa/niot/down-load
