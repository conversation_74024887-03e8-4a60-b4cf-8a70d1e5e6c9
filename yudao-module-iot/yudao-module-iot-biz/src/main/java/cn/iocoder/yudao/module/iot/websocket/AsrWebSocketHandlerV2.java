package cn.iocoder.yudao.module.iot.websocket;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.framework.common.util.IpUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.infra.api.file.dto.FileRespDTO;
import cn.iocoder.yudao.module.iot.config.BaiduAsrProperties;
import cn.iocoder.yudao.module.iot.config.TaskExecutorConfig;
import cn.iocoder.yudao.module.iot.dal.dataobject.ai.LogHistory;
import cn.iocoder.yudao.module.iot.dal.mongo.LogHistoryRepository;
import cn.iocoder.yudao.module.iot.enums.ai.AiApiNameConstants;
import cn.iocoder.yudao.module.iot.util.AudioUtil;
import cn.iocoder.yudao.module.iot.util.ProductUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.net.URI;
import java.nio.ByteBuffer;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class AsrWebSocketHandlerV2 implements WebSocketHandler {

    private static final String MESSAGE_TYPE_START = "start";
    private static final String MESSAGE_TYPE_FINISH = "finish";
    private static final String TYPE_FIELD = "type";
    private static final String SN_ATTRIBUTE = "sn";
    private static final int CONNECTION_TIMEOUT_SECONDS = 5;
    private static final int RETRY_INTERVAL_MS = 1;
    private static final String AUDIO_FORMAT = "pcm";
    private static final int SAMPLE_RATE = 16000;
    private static final long MAX_BUFFER_SIZE = 100 * 1024 * 1024;

    @Resource
    private BaiduAsrProperties baiduAsrProperties;
    @Resource
    private LogHistoryRepository logHistoryRepository;

    // 使用builder模式创建不可变的初始容量
    public static final ConcurrentHashMap<String, WebSocketClient> asrClients = new ConcurrentHashMap<>(16);
    public static final ConcurrentHashMap<String, WebSocketSession> clientSessions = new ConcurrentHashMap<>(16);
    private static final Map<String, ByteArrayOutputStream> sessionBuffers = new ConcurrentHashMap<>();
    private static final Map<String, List<LogHistory.Message>> reqMessages = new ConcurrentHashMap<>();
    private static final Map<String, List<LogHistory.Message>> respMessages = new ConcurrentHashMap<>();
    private static final Map<String, Long> timeTotals = new ConcurrentHashMap<>();
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        clientSessions.put(sessionId, session);
        String sn = Optional.ofNullable(session.getAttributes().get(SN_ATTRIBUTE))
                .map(Object::toString)
                .orElseGet(() -> java.util.UUID.randomUUID().toString());

        AsrWebSocketHandlerV2.AsrWebSocketClient asrClient = (AsrWebSocketHandlerV2.AsrWebSocketClient) session.getAttributes().get("asrClient");
        asrClient.setClientSession(session);
        asrClients.put(sessionId, asrClient);
    }


    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String sessionId = session.getId();
        AsrWebSocketClient asrClient = (AsrWebSocketClient) session.getAttributes().get("asrClient");
        if (asrClient == null || !asrClient.isOpen()) {
            log.warn("No active ASR client found for session: {}", sessionId);
            return;
        }

        handleWebSocketMessage(session,message, asrClient);
    }

    private void handleWebSocketMessage(WebSocketSession session,WebSocketMessage<?> message, WebSocketClient asrClient) {
        try {
            if (message instanceof BinaryMessage) {
                handleBinaryMessage( session, (BinaryMessage) message, asrClient);
            } else if (message instanceof TextMessage) {
                handleTextMessage(session,(TextMessage) message, asrClient);
            }
        } catch (Exception e) {
            log.error("Error handling message: ", e);
        }
    }

    @SneakyThrows
    private void handleBinaryMessage(WebSocketSession session, BinaryMessage message, WebSocketClient asrClient) {

        String uuid= (String) session.getAttributes().get(SN_ATTRIBUTE);
        ByteArrayOutputStream buffer = sessionBuffers.computeIfAbsent(uuid,
                k -> new ByteArrayOutputStream(256 * 1024)); // 初始容量1MB
        if (buffer.size() + message.getPayloadLength() > MAX_BUFFER_SIZE) {
            log.warn("Buffer size limit exceeded for session {}", uuid);
            return;
        }
        ByteBuffer payload = message.getPayload();
        byte[] audio = new byte[payload.remaining()];
        payload.get(audio);
        buffer.write(audio);
        asrClient.send(message.getPayload().array());
    }

    private void handleTextMessage(WebSocketSession session,TextMessage message, WebSocketClient asrClient) {
        String uuid= (String) session.getAttributes().get(SN_ATTRIBUTE);
        List<LogHistory.Message>messages= reqMessages.computeIfAbsent(uuid, k->new ArrayList<>());
        String payload = message.getPayload();
        Long ts=System.currentTimeMillis();
        log.info("Received text message:{} {}",uuid, payload);
        payload=payload.replace("fxzsos","dueros");
        timeTotals.put(uuid,ts);
        messages.add(new LogHistory.Message(System.currentTimeMillis(),payload));
        JSONObject jsonMessage = JSON.parseObject(payload);
        String type = jsonMessage.getString(TYPE_FIELD);
        if (MESSAGE_TYPE_START.equals(type)) {
            sendStartFrame(session,asrClient, payload);
        } else {
            asrClient.send(payload);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("WebSocket transport error", exception);
        cleanupSession(session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) {
        log.info("WebSocket connection closed with status: {}", closeStatus);
        cleanupSession(session);
    }

    @Resource
    private FileApi fileApi;
    public  void cleanupSession(WebSocketSession session) {
        String sessionId = session.getId();
        String uuid= (String) session.getAttributes().get(SN_ATTRIBUTE);
        if(sessionBuffers.get(uuid)!=null){
            byte[] datas=sessionBuffers.get(uuid).toByteArray();
            sessionBuffers.remove(uuid);
            TaskExecutorConfig.getInstance().execute(() -> {
                LogHistory logHistory=new LogHistory();
                logHistory.setId(IdUtil.objectId());
                logHistory.setTraceId(uuid);
                String productId= (String) session.getAttributes().get("productId");
                String deviceId= (String) session.getAttributes().get("deviceId");
                String deviceNo= (String) session.getAttributes().get("deviceNo");
                Integer sample= (int) session.getAttributes().get("sample");
                if(sample==null){
                    sample = SAMPLE_RATE;
                }
                byte[] saveData= AudioUtil.addWavHeader(sample,datas);
                Date date=new Date();
                long ts = date.getTime();
                String fileName=ts+"_asrchat.wav";
                String path = ProductUtils.getLogFilePath(productId,deviceId);
                FileRespDTO fileRespDTO= fileApi.createFileWithDetail(path,fileName,fileName,saveData);
                if(fileRespDTO!=null){
                    logHistory.setReqFile(new LogHistory.FileRecord(ts,fileRespDTO.getSize(),fileRespDTO.getType(),fileRespDTO.getUrl()));
                }
                Long start=  timeTotals.get(uuid);
                if(start!=null){
                    logHistory.setTotalCost(ts-start);
                }
                logHistory.setDeviceId(deviceId);
                logHistory.setProductId(productId);
                logHistory.setDeviceNo(deviceNo);
                logHistory.setApiName(AiApiNameConstants.ASR_CHAT);
                logHistory.setReqMsgs(reqMessages.get(uuid));
                logHistory.setRespMsgs(respMessages.get(uuid));
                logHistory.setCreateTime(date);
                log.info("保存chatHistory到mongodb {} {}",uuid,logHistory.getId());
                logHistoryRepository.save(logHistory);
                reqMessages.remove(uuid);
                respMessages.remove(uuid);
                timeTotals.remove(uuid);
            });
        }

        WebSocketClient asrClient = asrClients.remove(sessionId);
        if (asrClient != null) {
            asrClient.close();
        }

        clientSessions.remove(sessionId);

        try {
            session.close();
        } catch (Exception e) {
            log.warn("Error closing session", e);
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    private WebSocketClient createAsrClient(WebSocketSession clientSession, String sn) {
        try {
            String wsUrl = buildWsUrl(sn);
            AsrWebSocketClient asrWebSocketClient= new AsrWebSocketClient(new URI(wsUrl), clientSession, sn);
            return asrWebSocketClient;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create ASR WebSocket client", e);
        }
    }

    private String buildWsUrl(String sn) {
        return String.format("%s?sn=%s", baiduAsrProperties.getServerUrl(), sn);
    }

    private JSONObject buildAIDeviceInfoContext(WebSocketSession session){

        JSONObject deviceInfoContext=new JSONObject();
        JSONObject namespace= new JSONObject();
        namespace.put("namespace","ai.fxzsos.device_interface.ai_device_info");
        namespace.put("name","AiDeviceInfo");
        JSONObject payload= new JSONObject();
        payload.put("x_ai_vid",session.getAttributes().get("productId"));
        String deviceId = (String) session.getAttributes().get("deviceId");
        if(deviceId==null){
            deviceId = (String) session.getAttributes().get("deviceNo");
        }
        payload.put("x_ai_uid",deviceId);
        payload.put("x_ai_ip",IpUtils.getIPAddress(session.getHandshakeHeaders()));
        payload.put("model","jiutian-raw");
        deviceInfoContext.put("header",namespace);
        deviceInfoContext.put("payload",payload);
        return deviceInfoContext;
    }
    private void sendStartFrame(WebSocketSession session,WebSocketClient asrClient, String payload) {
        JSONObject data = JSON.parseObject(payload);
        JSONObject startFrame = data.getJSONObject("data");
        if(startFrame==null){
            startFrame=new JSONObject();
        }
        String clientIp= IpUtils.getIPAddress(session.getHandshakeHeaders());
        if(clientIp!=null){
            startFrame.put("client_ip",clientIp);
        }
        String productId= (String) session.getAttributes().get("productId");
        if(baiduAsrProperties.getLaikuProductId().equals(productId)){
            startFrame.put("client_id",baiduAsrProperties.getLaikuAppId());
            startFrame.put("appid",baiduAsrProperties.getLaikuAppId());
            startFrame.put("appkey",baiduAsrProperties.getLaikuAppKey());
            startFrame.put("rc_version",baiduAsrProperties.getLaikuVersion());
        }else{
            startFrame.put("client_id",baiduAsrProperties.getAppid());
            startFrame.put("appid", baiduAsrProperties.getAppid());
            startFrame.put("appkey", baiduAsrProperties.getAppkey());
        }

        if(baiduAsrProperties.getYinlianProductId().equals(productId)){
            startFrame.put("rc_version",baiduAsrProperties.getYinlianVersion());
        }

        startFrame.put("pid", baiduAsrProperties.getPid());
        JSONArray clientContextArr= startFrame.getJSONArray("client_context");
        if(clientContextArr==null){
            clientContextArr=new JSONArray();
        }
        clientContextArr.add(buildAIDeviceInfoContext(session));
        String deviceId= (String) session.getAttributes().get("deviceId");
        if(deviceId==null){
            deviceId=IdUtil.randomUUID();
        }
        if(data.getString("cuid")==null){
            startFrame.put("cuid",deviceId);
        }
        if(startFrame.getString("format")==null){
            startFrame.put("format", AUDIO_FORMAT);
        }
        if(startFrame.getString("sample")==null){
            startFrame.put("sample", SAMPLE_RATE);
        }
        data.put(TYPE_FIELD, MESSAGE_TYPE_START);
        session.getAttributes().put("sample",startFrame.get("sample"));
        data.put("data",startFrame);
        log.info("asrv2 sending start frame: {}", data);
        asrClient.send(data.toJSONString());
    }

    @Slf4j
    public static class AsrWebSocketClient extends WebSocketClient {
        @Getter
        @Setter
        private  WebSocketSession clientSession;
        private final String sn;

        public AsrWebSocketClient(URI serverUri, WebSocketSession clientSession, String sn) {
            super(serverUri);
            this.clientSession = clientSession;
            this.sn = sn;
        }

        @Override
        public void onOpen(ServerHandshake handshakedata) {
            log.info("ASR WebSocket connection established for sn: {}", sn);
        }

        @Override
        public void onMessage(String message) {
            try {
                String uuid= (String) clientSession.getAttributes().get(SN_ATTRIBUTE);
                message = message.replace("dueros","fxzsos");
                clientSession.sendMessage(new TextMessage(message));
                List<LogHistory.Message>messages= respMessages.computeIfAbsent(uuid, k->new ArrayList<>());
                log.info("Received text message:{} {}",uuid, message);
                messages.add(new LogHistory.Message(System.currentTimeMillis(),message));
            } catch (Exception e) {
                log.error("Error forwarding ASR message to client", e);
            }
        }

        @SneakyThrows
        @Override
        public void onClose(int code, String reason, boolean remote) {
            log.info("ASR WebSocket connection closed: code={}, reason={}, remote={}, sn={}",
                    code, reason, remote, sn);
            WebSocketSession session= clientSessions.get(clientSession.getId());
            if(session!=null && session.isOpen()){
                session.close();
                clientSessions.remove(clientSession.getId());
            }
        }

        @SneakyThrows
        @Override
        public void onError(Exception ex) {
            log.error("ASR WebSocket error for sn: " + sn, ex);
            WebSocketSession session= clientSessions.get(clientSession.getId());
            if(session!=null && session.isOpen()){
                session.close();
                clientSessions.remove(clientSession.getId());
            }
        }
    }

}
