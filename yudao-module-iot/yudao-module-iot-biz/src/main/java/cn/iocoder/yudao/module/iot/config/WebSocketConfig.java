package cn.iocoder.yudao.module.iot.config;

import cn.iocoder.yudao.module.iot.websocket.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    private final AsrWebSocketHandler asrWebSocketHandler;
    private final WebSocketInterceptor webSocketInterceptor;
    private final TranslateWebSocketInterceptor translateWebSocketInterceptor;
    private final AsrTranslateWebSocketHandler asrTranslateWebSocketHandler;
    private final AsrWebSocketHandlerV2 asrWebSocketHandlerV2;
    private final AzureTranslateWebSocketHandler azureTranslateWebSocketHandler;
    private final AzureWebSocketInterceptor azureWebSocketInterceptor;
    private final WebSocketInterceptorV2 webSocketInterceptorV2;
    private final LongAsrWebSocketHandler longAsrWebSocketHandler;
    private final LongAsrWebSocketInterceptor longAsrWebSocketInterceptor;
    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        container.setMaxTextMessageBufferSize(1024 * 1024); // 64KB
        container.setMaxBinaryMessageBufferSize(1024 * 1024); // 1024KB
        container.setMaxSessionIdleTimeout(15 * 60 * 1000L); // 可选：设置会话超时时间
        return container;
    }
    public WebSocketConfig(AsrWebSocketHandler asrWebSocketHandler,AsrTranslateWebSocketHandler translateWebSocketHandler, WebSocketInterceptor webSocketInterceptor, AsrWebSocketHandlerV2 asrWebSocketHandlerV2
            ,AzureTranslateWebSocketHandler azureTranslateWebSocketHandler,AzureWebSocketInterceptor azureWebSocketInterceptor,
                          TranslateWebSocketInterceptor translateWebSocketInterceptor,WebSocketInterceptorV2 webSocketInterceptorV2,
                           LongAsrWebSocketInterceptor longAsrWebSocketInterceptor,
                           LongAsrWebSocketHandler longAsrWebSocketHandler) {
        this.asrWebSocketHandler = asrWebSocketHandler;
        this.webSocketInterceptor = webSocketInterceptor;
        this.asrTranslateWebSocketHandler=translateWebSocketHandler;
        this.asrWebSocketHandlerV2 =asrWebSocketHandlerV2;
        this.azureTranslateWebSocketHandler = azureTranslateWebSocketHandler;
        this.azureWebSocketInterceptor =azureWebSocketInterceptor;
        this.translateWebSocketInterceptor = translateWebSocketInterceptor;
        this.webSocketInterceptorV2 = webSocketInterceptorV2;
        this.longAsrWebSocketHandler =longAsrWebSocketHandler;
        this.longAsrWebSocketInterceptor = longAsrWebSocketInterceptor;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(asrWebSocketHandler, "/app-ws/v1/asr")
                .addInterceptors(webSocketInterceptor)
                .setAllowedOrigins("*");
        registry.addHandler(asrWebSocketHandlerV2, "/app-ws/v2/asr")
                .addInterceptors(webSocketInterceptorV2)
                .setAllowedOrigins("*");
        // 注册第二个 handler
        registry.addHandler(asrTranslateWebSocketHandler, "/app-ws/v1/realtime_speech_trans")
                .addInterceptors(translateWebSocketInterceptor)
                .setAllowedOrigins("*");

        registry.addHandler(azureTranslateWebSocketHandler, "/app-ws/v2/realtime_speech_trans")
                .addInterceptors(azureWebSocketInterceptor)
                .setAllowedOrigins("*");
        registry.addHandler(longAsrWebSocketHandler,"/app-ws/v1/long-asr")
                .addInterceptors(longAsrWebSocketInterceptor)
                .setAllowedOrigins("*");

    }
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
}
