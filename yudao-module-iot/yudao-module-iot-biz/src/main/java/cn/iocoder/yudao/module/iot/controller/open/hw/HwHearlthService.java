package cn.iocoder.yudao.module.iot.controller.open.hw;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.date.DateUtils;
import cn.iocoder.yudao.module.iot.config.HuaweiProperties;
import cn.iocoder.yudao.module.iot.controller.open.hw.vo.*;
import cn.iocoder.yudao.module.iot.dal.dataobject.hw.HuaweiOAuthTokenDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.hw.HwUserHealthDO;
import cn.iocoder.yudao.module.iot.dal.mongo.HuaweiOAuthTokenRepository;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.google.common.collect.Lists;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BiConsumer;

@Slf4j
@Service
public class HwHearlthService {
    private RestTemplate restTemplate=new RestTemplate();
    @Setter
    @Resource
    private HuaweiProperties huaweiProperties;

    public List<JSONObject> dailyActivitySummary(String accessToken,String startDay,String endDay){
        JSONObject body = new JSONObject();
        body.put("startDay", startDay);
        body.put("endDay", endDay);
        body.put("timeZone", "+0800");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + accessToken);
        headers.set("x-client-id", huaweiProperties.getClientId());
        headers.set("x-version", "1");
        log.info("req dailyActivitySummary body is {} ", body.toString());
        // 构建请求实体
        HttpEntity<String> requestEntity = new HttpEntity<>(body.toJSONString(), headers);
        String url = "https://health-api.cloud.huawei.com/healthkit/v2/sampleSet:dailyActivitySummary";
        RestTemplate restTemplate = new RestTemplate();

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            String resp = response.getBody();
            log.info("req dailyPolymerize resp is {} ", resp);
            List<JSONObject>inputData= JSON.parseArray(resp, JSONObject.class);

            List<JSONObject> result = new ArrayList<>();

            // 遍历输入数据
            for (JSONObject data : inputData) {
                JSONArray groupArray = data.getJSONArray("dailyActivitySummary");

                // 遍历每个group
                for (int i = 0; i < groupArray.size(); i++) {
                    JSONObject group = groupArray.getJSONObject(i);
                    JSONObject dayData = new JSONObject();

                    // 获取并转换日期
                    long startTime = group.getLongValue("startTime")/1000000;
                    LocalDateTime dateTime = LocalDateTime.ofInstant(
                            Instant.ofEpochMilli(startTime),
                            ZoneId.systemDefault()
                    );
                    String dateStr = dateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                    dayData.put("date", dateStr);
                    dayData.put("dayOfWeek",DateUtils.getDayOfWeekFromTimestamp(startTime));
                    // 获取步数
                    List<JSONObject> valueArray = group.getJSONArray("value").toJavaList(JSONObject.class);
                    if (valueArray != null && !valueArray.isEmpty()) {
                        valueArray.forEach(jsonObject -> {
                            String fieldName=jsonObject.getString("fieldName");
                            String valueKey = jsonObject.keySet().stream().filter(s -> !s.equals("fieldName")).findFirst().get();
                            Object value=jsonObject.get(valueKey);
                            dayData.put(fieldName,value);
                        });

                    }
                    result.add(dayData);
                }
            }

            return result;

        } catch (RestClientException e) {
            log.error("Request failed", e);
            throw new RuntimeException("Request failed", e);
        }
    }
    @Cacheable(value = "dailyPolymerizeOfLastTendays", key = "#openId", unless = "#result == null")
    public List<Map> dailyPolymerizeOfLastTendays(String openId){
        HuaweiOAuthTokenDO huaweiOAuthTokenDO = getHuaweiOAuthToken(openId);
        if (huaweiOAuthTokenDO == null) return null;
        List<String> dataTypes=HwUtil.getDailyPolymerizeDataTypes(huaweiOAuthTokenDO.getScopes());
        Long today=System.currentTimeMillis();
        Long tenDayAgo=today-3600*24*1000*10;
        String endDay=DateUtils.getCurrentDate(today);
        String startDay=DateUtils.getCurrentDate(tenDayAgo);
        return dailyPolymerize(huaweiOAuthTokenDO.getAccessToken(),dataTypes,startDay,endDay);
    }

    @Nullable
    private HuaweiOAuthTokenDO getHuaweiOAuthToken(String openId) {
        HuaweiOAuthTokenDO huaweiOAuthTokenDO = huaweiOAuthTokenRepository.findFirstByOpenidOrderByCreatedAtDesc(openId);
        if(huaweiOAuthTokenDO==null){
            return null;
        }
        Long accessTokenUpdateAt = huaweiOAuthTokenDO.getAccessTokenUpdateAt();
        Long now = System.currentTimeMillis();
        if (now - accessTokenUpdateAt > huaweiOAuthTokenDO.getExpiresIn() * 1000 *0.8) {
            HuaweiTokenRespVo huaweiTokenRespVo = this.getNewTokenByRefreshToken(huaweiOAuthTokenDO.getRefreshToken());
            if (huaweiTokenRespVo == null) {
                return null;
            }
            huaweiOAuthTokenDO.setAccessToken(huaweiTokenRespVo.getAccess_token());
            huaweiOAuthTokenDO.setAccessTokenUpdateAt(System.currentTimeMillis());
            huaweiOAuthTokenRepository.save(huaweiOAuthTokenDO);
        }
        return huaweiOAuthTokenDO;
    }

    public List<Map> dailyPolymerize(String accessToken,List<String> dataTypes,String startDay,String endDay){
        JSONObject body = new JSONObject();
        body.put("dataTypes", dataTypes);
        body.put("startDay", startDay);
        body.put("endDay", endDay);
        body.put("timeZone", "+0800");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + accessToken);
        headers.set("x-client-id", huaweiProperties.getClientId());
        headers.set("x-version", "1");
        log.info("req dailyPolymerize body is {} ", body.toString());
        // 构建请求实体
        HttpEntity<String> requestEntity = new HttpEntity<>(body.toJSONString(), headers);
        String url = "https://health-api.cloud.huawei.com/healthkit/v2/sampleSet:dailyPolymerize";
        RestTemplate restTemplate = new RestTemplate();

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            String resp = response.getBody();
            log.info("req dailyPolymerize resp is {} ", resp);
            List<JSONObject>inputData= JSON.parseArray(resp, JSONObject.class);

            List<Map> result = new ArrayList<>();

            // 遍历输入数据
            for (JSONObject data : inputData) {
                JSONArray groupArray = data.getJSONArray("group");

                // 遍历每个group
                for (int i = 0; i < groupArray.size(); i++) {
                    JSONObject group = groupArray.getJSONObject(i);
                    Map dayData = new LinkedHashMap();

                    // 获取并转换日期
                    long startTime = group.getLongValue("startTime");
                    LocalDateTime dateTime = LocalDateTime.ofInstant(
                            Instant.ofEpochMilli(startTime),
                            ZoneId.systemDefault()
                    );
                    String dateStr = dateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                    dayData.put("date", dateStr);
                    dayData.put("dayOfWeek",DateUtils.getDayOfWeekFromTimestamp(startTime));
                    // 获取步数
                    List<JSONObject> sampleSet = group.getJSONArray("sampleSet").toJavaList(JSONObject.class);
                    if (sampleSet != null && !sampleSet.isEmpty()) {
                        sampleSet.stream().forEach(sample -> {
                            List<JSONObject> samplePoints = sample.getJSONArray("samplePoints").toJavaList(JSONObject.class);
                            if (samplePoints != null && !samplePoints.isEmpty()) {
                                for(int j=0;j<samplePoints.size();j++){
                                    JSONObject samplePoint=samplePoints.get(j);
                                    JSONArray valueArray= samplePoint.getJSONArray("value");
                                    if(valueArray!=null && !valueArray.isEmpty()){
                                        String dataTypeName=samplePoint.getString("dataTypeName");
                                        log.info("key {} valueArray {}",dataTypeName,valueArray.toString());
                                        for(int k=0;k<valueArray.size();k++){
                                            JSONObject valueObj= valueArray.getJSONObject(k);
                                            String fieldName=valueObj.getString("fieldName");
                                            if(dataTypeName.equals("com.huawei.continuous.body.temperature.statistics")){
                                                fieldName="temperature_"+fieldName;
                                            }else if(dataTypeName.equals("com.huawei.continuous.blood_glucose.statistics")){
                                                fieldName="blood_glucose_"+fieldName;
                                            }else if(dataTypeName.equals("com.huawei.continuous.heart_rate.statistics")){
                                                fieldName="heart_rate_"+fieldName;
                                            }else if(dataTypeName.equals("com.huawei.continuous.steps.total") && fieldName.equals("duration")){
                                                fieldName = "step_minutes";
                                            }else if(dataTypeName.equals("com.huawei.emotion.statistics")){
                                                fieldName="emotion_"+fieldName;
                                            }else if("com.huawei.instantaneous.stress.statistics".equals(dataTypeName)){
                                                fieldName="stress_"+fieldName;
                                            }else if("com.huawei.continuous.exercise_intensity.v2.statistics".equals(dataTypeName)){
                                                if(fieldName.equals("totalIntensity")){
                                                    fieldName="exercise_intensity_minutes_total";
                                                }else if(fieldName.equals("intensity_map")){
                                                    continue;
                                                }
                                            }else if("com.huawei.continuous.body_weight.statistics".equals(dataTypeName)){
                                                continue;
                                            }else if ("com.huawei.continuous.altitude.statistics".equals(dataTypeName)){
                                                fieldName ="altitude"+fieldName;
                                            }
                                            String newfield=valueObj.keySet().stream().filter(s -> !Objects.equals("fieldName",s)).findFirst().get();
                                            Object fieldValue=valueObj.get(newfield);
                                            dayData.put(fieldName,fieldValue);
                                        }
                                    }
                                }
                            }
                        });

                    }

                    result.add(dayData);
                }
            }

            return result;

        } catch (RestClientException e) {
            log.error("Request failed", e);
            return null;
        }
    }

    public HuaweiTokenRespVo getNewTokenByRefreshToken(String refreshToken){
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("grant_type", "refresh_token");
            params.add("client_id", huaweiProperties.getClientId());
            params.add("client_secret", huaweiProperties.getClientSecret());
            params.add("refresh_token", refreshToken);
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(huaweiProperties.getTokenUrl(), request, String.class);
            String body = response.getBody();
            log.info("获取刷新token 响应{}",body);
            return JSON.parseObject(body, HuaweiTokenRespVo.class);
        }catch (HttpClientErrorException e){
            return null;
        }
    }

    public HuaweiTokenInfoRespVo getClientTokenInfo(String accessToken) {

        try {
            String baseUrl = "https://oauth-api.cloud.huawei.com/rest.php";

            StringBuffer stringBuffer = new StringBuffer(baseUrl);
            stringBuffer.append("?nsp_svc=");
            stringBuffer.append("huawei.oauth2.user.getTokenInfo");
            stringBuffer.append("&open_id=");
            stringBuffer.append("OPENID");
            stringBuffer.append("&access_token=");
            stringBuffer.append(URLEncoder.encode(accessToken, "UTF-8"));

            HttpResponse response = HttpRequest.get(stringBuffer.toString())
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .execute();
            String body=response.body();
            log.info("hw get client info resp {}",body);
            HuaweiTokenInfoRespVo respVo = JSON.parseObject(body,HuaweiTokenInfoRespVo.class);
            if(StringUtils.isBlank(respVo.getOpen_id())){
                return null;
            }


            return respVo;
        }catch (Exception e){
            return null;
        }
    }
    @Resource
    private HuaweiOAuthTokenRepository huaweiOAuthTokenRepository;


    @Cacheable(value = "userHealth", key = "#openId", unless = "#result == null")
    public CommonResult<HwUserHealthDO> getSamplePointsByOpenId(String openId) {
        HuaweiOAuthTokenDO huaweiOAuthTokenDO = getHuaweiOAuthToken(openId);
        if(huaweiOAuthTokenDO==null){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        HuaweiTokenInfoRespVo respVo = this.getClientTokenInfo(huaweiOAuthTokenDO.getAccessToken());
        if (respVo == null) {
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        if(StringUtils.isBlank(respVo.getScope())){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        huaweiOAuthTokenDO.setScopes(Arrays.asList(StringUtils.split(respVo.getScope(), " ")));

        return getSamplePoints(huaweiOAuthTokenDO.getAccessToken(), huaweiOAuthTokenDO.getScopes());
    }

    @Cacheable(value = "getActivityRecordsByOpenId", key = "#openId", unless = "#result == null")
    public List<SportRecordVo> getActivityRecordsByOpenId(String openId){
        HuaweiOAuthTokenDO huaweiOAuthTokenDO = getHuaweiOAuthToken(openId);
        if(huaweiOAuthTokenDO==null){
            return null;
        }
        List<SportRecordVo> recordVos= this.getActivityRecords(huaweiOAuthTokenDO.getAccessToken());
        if (recordVos == null) {
            return null;
        }


        return getActivityRecords(huaweiOAuthTokenDO.getAccessToken());
    }

    public List<SportRecordVo> getActivityRecords(String accessToken){
        try {

            Long endTime=System.currentTimeMillis();
            Long startTime=endTime-3600*24*10*1000;
            String baseUrl = "https://health-api.cloud.huawei.com/healthkit/v2/activityRecords";
            StringBuffer stringBuffer = new StringBuffer(baseUrl)
            .append("?startTime=").append(startTime)
            .append("&endTime=").append(endTime)
            .append("&sourceType=0&sourceType=1&sourceType=2&sourceType=3&sourceType=4&sourceType=5");
            HttpResponse response = HttpRequest.get(stringBuffer.toString())
                    .header("Authorization", "Bearer "+accessToken)
                    .header("x-client-id",huaweiProperties.getClientId())
                    .header("x-version","1")
                    .execute();
            String resp=response.body();
            log.info("请求锻炼数据,请求参数:{} {} 返回参数:{} ",stringBuffer.toString(),accessToken, resp);
            List<SportRecordVo> recordVos=SportHealthDataParser.parseActivityRecords(resp);
            return recordVos;
        }catch (Exception e){
            return null;
        }

    }
    public CommonResult<GetCheckInfoVo> getUserInfo(String openId){
        HuaweiOAuthTokenDO huaweiOAuthTokenDO = getHuaweiOAuthToken(openId);
        if(huaweiOAuthTokenDO==null){
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        HuaweiTokenInfoRespVo respVo = this.getClientTokenInfo(huaweiOAuthTokenDO.getAccessToken());
        if (respVo == null) {
            return CommonResult.error(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        huaweiOAuthTokenDO.setScopes(Arrays.asList(StringUtils.split(respVo.getScope(), " ")));
        GetCheckInfoVo checkInfoVo=new GetCheckInfoVo();
        checkInfoVo.setNickname(huaweiOAuthTokenDO.getNickname());
        checkInfoVo.setOpenid(huaweiOAuthTokenDO.getOpenid());
        checkInfoVo.setPicture(huaweiOAuthTokenDO.getPicture());
        checkInfoVo.setDisplay_name(huaweiOAuthTokenDO.getDisplay_name());
        checkInfoVo.setUnionid(huaweiOAuthTokenDO.getUnionid());
        List<String>unAuthScopes=HwUtil.getAllUnAuthScopes(huaweiOAuthTokenDO.getScopes());
        checkInfoVo.setUn_auth_scopes(unAuthScopes);
        checkInfoVo.setFull_auth(unAuthScopes.isEmpty());
        ThreadUtil.execAsync(() -> {
            huaweiOAuthTokenDO.setExpiresIn(respVo.getExpire_in());
            log.info("更新oauthToken {}",JSON.toJSONString(huaweiOAuthTokenDO));
           huaweiOAuthTokenRepository.save(huaweiOAuthTokenDO);
        });

        return CommonResult.success(checkInfoVo);

    }

    public CommonResult<HwUserHealthDO> getSamplePoints(String token, List<String> scopes) {
        String url = "https://health-api.cloud.huawei.com/healthkit/v2/sampleSets/latestSamplePoint";
        List<String> dataTypes = HwUtil.getHealthSampleDataTypesByScopes(scopes);
        StringBuilder urlBuilder = new StringBuilder(url);
        if (dataTypes != null && !dataTypes.isEmpty()) {
            urlBuilder.append("?");
            for (int i = 0; i < dataTypes.size(); i++) {
                if (i > 0) urlBuilder.append("&");
                urlBuilder.append("dataType=").append(URLEncoder.encode(dataTypes.get(i)));
            }
        }
        String fullUrl = urlBuilder.toString();
        // 发起请求
        log.info("full url is {}", fullUrl);
        HttpResponse response = HttpRequest.get(fullUrl)
                .header("Authorization", "Bearer " + token)
                .header("x-client-id", huaweiProperties.getClientId())
                .header("x-version", "1")
                .contentType(MediaType.APPLICATION_JSON_UTF8_VALUE)
                .execute();
        if (response.isOk()) {
            JSONObject result = JSONObject.parseObject(response.body());
            log.info("latestSamplePoint result is {}", result);
            String parseStr = parse(result.toJSONString());
            HwUserHealthDO userHealthDO = JSON.parseObject(parseStr, HwUserHealthDO.class);
            userHealthDO.setScopes(scopes);
            return CommonResult.success(userHealthDO);
        } else {
            log.info(response.body());
            return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
        }
    }

    public static String parse(String json) {
        Map<String, Object> result = new LinkedHashMap<>();
        JSONObject root = JSON.parseObject(json).getJSONObject("samplePoints");

        Set<String> keys = root.keySet();
        for (String key : keys) {
            JSONObject node = root.getJSONObject(key);

            // 取 dataTypeName 最后一个词
            String dataTypeName = node.getString("dataTypeName");
            String[] parts = dataTypeName.split("\\.");
            String fieldKey = parts[parts.length - 1];

            // 解析 value 数组
            JSONArray valueArray = node.getJSONArray("value");
            if (valueArray != null) {
                for (int i = 0; i < valueArray.size(); i++) {
                    JSONObject valueNode = valueArray.getJSONObject(i);
                    String fieldName = valueNode.getString("fieldName");
//                    String key= valueNode.keySet().stream().filter(s -> !Objects.equals(s,"fieldName")).findFirst().get();
                    if (valueNode.containsKey("floatValue")) {
                        result.put(fieldKey + "_" + fieldName, valueNode.getDouble("floatValue"));
                    } else if (valueNode.containsKey("integerValue")) {
                        result.put(fieldKey + "_" + fieldName, valueNode.getIntValue("integerValue"));
                    } else if (valueNode.containsKey("stringValue")) {
                        result.put(fieldKey + "_" + fieldName, valueNode.getString("stringValue"));
                    }
                }
            }
        }
        return JSON.toJSONString(result);
    }
    @Cacheable(value = "getSleepRecordsByOpenId", key = "#openId", unless = "#result == null")
    public List<SleepVo> getSleepRecordsByOpenId(String openId){
        HuaweiOAuthTokenDO tokenDO=getHuaweiOAuthToken(openId);
        if (tokenDO == null) {
            return null;
        }
       return getSleepRecords(tokenDO.getAccessToken());
    }
    private static final Map<String, BiConsumer<SleepVo, JSONObject>> FIELD_HANDLERS = new HashMap<>();

    static {
        FIELD_HANDLERS.put("fall_asleep_time", (sleepVo, json) ->
                sleepVo.setFallAsleepTime(DateUtil.formatDateTime(new Date(json.getLong("longValue")))));
        FIELD_HANDLERS.put("wakeup_time", (sleepVo, json) ->
                sleepVo.setWakeupTime(DateUtil.formatDateTime(new Date(json.getLong("longValue")))));
        FIELD_HANDLERS.put("all_sleep_time", (sleepVo, json) ->
                sleepVo.setAllSleepMinutes(json.getInteger("integerValue")));
        FIELD_HANDLERS.put("light_sleep_time", (sleepVo, json) ->
                sleepVo.setLightSleepMinutes(json.getInteger("integerValue")));
        FIELD_HANDLERS.put("deep_sleep_time", (sleepVo, json) ->
                sleepVo.setDeepSleepMinutes(json.getInteger("integerValue")));
        FIELD_HANDLERS.put("dream_time", (sleepVo, json) ->
                sleepVo.setRapidEyeMovementMinutes(json.getInteger("integerValue")));
        FIELD_HANDLERS.put("awake_time", (sleepVo, json) ->
                sleepVo.setAwakeMinutes(json.getInteger("integerValue")));
        FIELD_HANDLERS.put("wakeup_count", (sleepVo, json) ->
                sleepVo.setWakeupCount(json.getInteger("integerValue")));
        FIELD_HANDLERS.put("deep_sleep_part", (sleepVo, json) ->
                sleepVo.setDeepSleepPart(json.getInteger("integerValue")));
        FIELD_HANDLERS.put("sleep_score", (sleepVo, json) ->
                sleepVo.setSleepScore(json.getInteger("integerValue")));
        FIELD_HANDLERS.put("sleep_efficiency", (sleepVo, json) ->
                sleepVo.setSleepEfficiency(json.getInteger("integerValue")));
    }

    public List<SleepVo> getSleepRecords(String accessToken){
        try {

            Long endTime=System.currentTimeMillis();
            Long startTime=endTime-3600*24*10*1000;
            String baseUrl = "https://health-api.cloud.huawei.com/healthkit/v2/healthRecords";
            StringBuffer stringBuffer = new StringBuffer(baseUrl);
            stringBuffer.append("?startTime=").append(startTime*1000000);
            stringBuffer.append("&endTime=").append(endTime*1000000);
            stringBuffer.append("&dataType=com.huawei.health.record.sleep");
//                    .append("&subDataType=com.huawei.continuous.sleep.fragment");
            HttpResponse response = HttpRequest.get(stringBuffer.toString())
                    .header("Authorization", "Bearer "+accessToken)
                    .header("x-client-id",huaweiProperties.getClientId())
                    .header("x-version","1")
                    .execute();
            String resp=response.body();
            log.info("请求睡眠时长,请求参数:{} {} 返回参数: {}",stringBuffer.toString(),accessToken, resp);
            JSONObject obj = JSON.parseObject(resp,JSONObject.class);
            JSONArray jsonArray = obj.getJSONArray("healthRecords");
            List<SleepVo>sleepList=new ArrayList<>();
            for(int i=0;i<jsonArray.size();i++) {
                List<JSONObject> values= jsonArray.getJSONObject(i).getJSONArray("value").toJavaList(JSONObject.class);
                SleepVo sleepVo=new SleepVo();
                values.stream().forEach(jsonObject -> {
                    String fieldName = jsonObject.getString("fieldName");
                    if(jsonObject.get("integerValue")==null || jsonObject.getInteger("integerValue")!=0){
                        BiConsumer<SleepVo, JSONObject> handler = FIELD_HANDLERS.get(fieldName);
                        if (handler != null) {
                            handler.accept(sleepVo, jsonObject);
                        }
                    }


                });
                sleepList.add(sleepVo);
            }
            return sleepList;
        }catch (Exception e){
            return null;
        }

    }
    public CommonResult cancelAuthorizationByOpenId(String openId){
        HuaweiOAuthTokenDO huaweiOAuthTokenDO = getHuaweiOAuthToken(openId);
        if(huaweiOAuthTokenDO==null){
            return CommonResult.success(true);
        }
        boolean res= cancelAuthorization(huaweiOAuthTokenDO.getAccessToken());
        return CommonResult.success(res);
    }

    public boolean cancelAuthorization(String accessToken) {

        try {
            String url = "https://health-api.cloud.huawei.com/healthkit/v2/consents/" + huaweiProperties.getClientId() + "?deleteData=false";
            HttpResponse response = HttpRequest.delete(url)
                    .header("Authorization", "Bearer " + accessToken)
                    .header("Content-Type", "application/json")
                    .header("x-client-id", huaweiProperties.getClientId())
                    .execute();
            log.info("cancelAuthorization resp is {}",response.body());
            if (response.isOk()) {

                return true;
            } else {

                return false;
            }

        } catch (Exception e) {
            return false;
        }
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        HwHearlthService hwHearlthService = new HwHearlthService();
        hwHearlthService.setHuaweiProperties(new HuaweiProperties());
//        String token = "DQEAAOi9euiBtoZsFBA2Ttr3kmaGvR8FRud0rEAV+aenF27MIHQ1C7f8kZvT0pCT8tM1PVXPtf5XCwGwPVvuIWtbp2EFwcQvUx6TM6g4db7wIdRmmX2ZGVM2STf6";
//        List<String> scopes = new ArrayList<>();
//        scopes.add("https://www.huawei.com/healthkit/bloodpressure.read");
//        scopes.add("https://www.huawei.com/healthkit/heightweight.read");
//        scopes.add("https://www.huawei.com/healthkit/oxygensaturation.read");
//
//        CommonResult result = hwHearlthService.getSamplePoints(token, scopes);
//        System.out.println(JSON.toJSONString(result.getData()));

//        JSONObject js=  hwHearlthService.getClientTokenInfo("DQEAAMQaHfqL37kj3N8N1jKsf8fH5eDQyMICxalmT1UhRUuqrsiROjIZZ9Ha7A76tbqZ94qztYZ0AFbYCwD+W3QdW5bORe9NjNcP/+1RtgTSYJw7l5xQrGx99GKX0JbrFKSCRaSjJEVXPi76+RIvbKh78/g+");
//        System.out.println(js.toString());
//        String refreshToken="DQECAGX5Zfhd52kmI0j6hjvfkct4wFyDsZT2ofiL8l+6Uir4Qxwh4KDZuHZEFDmB+h8WHLmwyQM9koKSTmrAJqmgafCFrlkY+5svI25VWio8kFL4qvLxFVXGBxNv1yI60SIJODKGgJXQP1a3eKfVqcwDHQncZ8POdvbq";
//        HuaweiTokenRespVo respVo=hwHearlthService.getNewTokenByRefreshToken(refreshToken);
//        System.out.println(JSON.toJSONString(respVo));
        String token="DQEAAHW5W6Ug1cg/SbNbr8yLZmu5RHYT8OcU7jU2Iswm8WNJHIJQ2TNUsLVd+GHsATjXf4bZi+FTaJfWp+/Zc9C2dqRDmPYY9tdvDXfCbz3tslXI1SmLbyxc3uiBpDMNmtvb+o0EPSEBFoNXmDnZtLkSLVCMVru4IBfQ";
         List<String> fields=Lists.newArrayList("com.huawei.continuous.steps.delta"
                 ,"com.huawei.continuous.calories.burnt"
                 ,"com.huawei.continuous.distance.delta",
                 "com.huawei.instantaneous.altitude",
                 "com.huawei.active_hours",
                 "com.huawei.continuous.exercise_intensity.v2",
                 "com.huawei.instantaneous.blood_pressure",
                 "com.huawei.instantaneous.body.temperature",
                 "com.huawei.instantaneous.spo2",
                 "com.huawei.instantaneous.heart_rate",
                 "com.huawei.instantaneous.stress",
                 "com.huawei.instantaneous.blood_glucose",
                 "com.huawei.instantaneous.body_weight",
                 "com.huawei.emotion");
////        List<JSONObject> resp=hwHearlthService.dailyActivitySummary(token,"20250510","20250520");
////        System.out.println(resp.toString());
//        List<JSONObject> resp1=hwHearlthService.dailyPolymerize(token,fields,"20250510","20250520");
//        System.out.println(resp1.toString());
        List<String> SCOPES=Lists.newArrayList(ScopeConstants.BLOOD_GLUCOSE_READ,ScopeConstants.HEIGHT_WEIGHT_READ,
                ScopeConstants.BLOOD_PRESSURE_READ,ScopeConstants.EMOTION_READ,ScopeConstants.BODY_TEMPERATURE_READ,
                ScopeConstants.HEART_HEALTH_READ,ScopeConstants.HEART_RATE_READ,ScopeConstants.OXYGEN_SATURATION_READ
                ,ScopeConstants.SLEEP_READ,ScopeConstants.STRESS_READ);
        System.out.println(JSON.toJSONString(hwHearlthService.getSamplePoints(token,SCOPES)));
//        String refreshToken="DQECACKLHn9ywUrKP0RSP/gQuMhLB+ovBxXHWbbEdueDcfin1oS1DluZuVgMnDqplv+Oju9MzMlW58+WbHMYNQPZwYu400wcRhZJ6RJYsXThRUwFApgLNh/jPI8JrC0aXyAolWUc8gDui7xbujuLcQ+LAqoz9aY0hhLJ";
//        System.out.println(hwHearlthService.getNewTokenByRefreshToken(refreshToken));
//        System.out.println(JSON.toJSONString(hwHearlthService.dailyPolymerize(token,fields,"20250523","20250603")));
        //System.out.println(JSON.toJSONString(hwHearlthService.getSleepRecords(token)));
//        System.out.println(JSON.toJSONString(hwHearlthService.getNewTokenByRefreshToken("DQECABapc5hQzS7OjntYRGVAGswGzYMZ2y/n7EKdLHE+swDyWq4avTdkjWUJ4pOpvLw9WGImIDNBoZqX96gBcbSxAktXAVob6UTCUwWXc1mCcJIZZ2R9kd1U7nNLnbCz704610Ggc3MGE7BRAQwsFS+3aZHutPrMHxcI")));

        System.out.println(JSON.toJSONString(hwHearlthService.getSamplePoints(token,SCOPES)));
//        System.out.println(hwHearlthService.cancelAuthorization("DQEAAA5/R1oZVxwHbdfVRfj0ImbpR+IOh8dXlmG+s00hX4QK0wVfVYDPim5Jt852Xu4zzvwMt0axryQpb3WovAr5RKxK4wkk08ZUZte3Dp0Qdg+gQqyujgfUqKX/M8xdkp9wnIM6nPVNitw7L7Xg5SvW63om"));
    }


    public void removeAllHwCahceByOpenId(String openId){
        CacheManager cacheManager= SpringUtil.getBean(CacheManager.class);
        List<String> cacheNames=Lists.newArrayList("dailyPolymerizeOfLastTendays","userHealth","getActivityRecordsByOpenId","getSleepRecordsByOpenId");
        for(String cacheName:cacheNames){
            cacheManager.getCache(cacheName).evict(openId);
        }
    }
}
