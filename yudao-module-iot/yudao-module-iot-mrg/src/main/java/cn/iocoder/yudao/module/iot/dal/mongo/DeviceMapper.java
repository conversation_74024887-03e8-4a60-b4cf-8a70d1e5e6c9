package cn.iocoder.yudao.module.iot.dal.mongo;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.module.iot.dal.dataobject.device.DeviceDO;
import cn.iocoder.yudao.module.iot.enums.device.DeviceStatusEnum;
import com.mongodb.bulk.BulkWriteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email: <EMAIL>
 * @date: 2022/7/8 16:06
 */
@Slf4j
@Service
public class DeviceMapper {
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private DeviceRepository deviceRepository;

    public List<DeviceDO> selectUserDevice(Long projectId, Long userId) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (projectId != null) {
            criteria.and(DeviceDO.COL_PROJECT_ID).is(projectId);
        }
        criteria.and(DeviceDO.COL_OWNER).is(String.valueOf(userId));
        query.addCriteria(criteria);
        List<DeviceDO> list = mongoTemplate.find(query, DeviceDO.class);
        return list;
    }

    public void batchUpdate(Collection<DeviceDO> deviceList) {
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, DeviceDO.COLLECTION_NAME);
        deviceList.forEach(deviceDO -> {
            deviceDO.setUpdateTime(new Date());
            Map<String, Object> dataMap = BeanUtil.beanToMap(deviceDO, false, true);
            Update update = new Update();
            dataMap.remove("cacheExpireUtc");
            dataMap.remove("id");
            if (dataMap.isEmpty()) {
                log.info("update deviceById dataMap is empty not update");
            } else {
                dataMap.forEach(update::set);
                Query query = new Query().addCriteria(Criteria.where(DeviceDO.COL_ID).is(deviceDO.getId()));
                operations.updateOne(query, update);
            }
        });
        BulkWriteResult result = operations.execute();
        log.info("batchUpdateDevice result {}", result.getModifiedCount());
    }

    public void updateById(DeviceDO deviceDO) {
        deviceDO.setUpdateTime(new Date());
        Map<String, Object> dataMap = BeanUtil.beanToMap(deviceDO, false, true);
        Update update = new Update();
        dataMap.remove("cacheExpireUtc");
        dataMap.remove("id");
        if (dataMap.isEmpty()) {
            log.info("update deviceById dataMap is empty not update");
            return;
        }
        // 遍历了 dataMap 中的属性，将非空的属性添加到 Update 对象中
        dataMap.forEach(update::set);
        Query query = new Query().addCriteria(Criteria.where(DeviceDO.COL_ID).is(deviceDO.getId()));
        mongoTemplate.update(DeviceDO.class).matching(query).apply(update).first();
    }

    public Boolean existsByProductId(Long productId) {
        Query query = new Query();
        query.addCriteria(Criteria.where(DeviceDO.COL_PRODUCT_ID).is(productId));
        return mongoTemplate.exists(query, DeviceDO.COLLECTION_NAME);
    }


    public List<DeviceDO> selectBatchIds(Collection<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where(DeviceDO.COL_ID).in(idList));
        List<DeviceDO> list = mongoTemplate.find(query, DeviceDO.class);
        return list;
    }

    public DeviceDO selectById(Long id) {
        return deviceRepository.findById(id).orElse(null);
    }

    public DeviceDO selectByProductKeyAndSn(String productKey, String sn) {
        return deviceRepository.findFirstByProductKeyAndSn(productKey, sn).orElse(new DeviceDO());
    }

    public DeviceDO selectByProductKeyAndSnCode(String productKey, String sn) {
        return deviceRepository.findFirstByProductKeyAndSnCode(productKey, sn).orElse(new DeviceDO());
    }

    public void deleteById(Long id) {
        deviceRepository.deleteById(id);
    }

    public List<DeviceDO> findOnlineDevicesOnlineTimeBefore(Date date) {
        Query query = new Query();
        Criteria criteria = Criteria.where(DeviceDO.COL_STATUS).is(DeviceStatusEnum.ONLINE.getStatus())
                .and(DeviceDO.COL_FROM).isNull()
                .and(DeviceDO.COL_ONLINE_TIME).lt(date);
        query.addCriteria(criteria);
        return mongoTemplate.find(query, DeviceDO.class);
    }

    public DeviceDO selectBySn(String sn) {
        return deviceRepository.findFirstBySn(sn).orElse(new DeviceDO());
    }

    public DeviceDO selectByMac(String mac){
        return deviceRepository.findByMac(mac).orElse(new DeviceDO());
    }
}
