package cn.iocoder.yudao.module.iot.dal.dataobject.ai;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

@Document(collection = "log_histories")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogHistory {
    @Id
    private String id;
    @Indexed(name = "idx_traceId")
    private String traceId;
    @Indexed(name = "idx_deviceId")
    private String deviceId;
    @Indexed(name = "idx_deviceno")
    private String deviceNo;
    @Indexed(name = "idx_productId")
    private String productId;
    private List<Message> reqMsgs;
    private List<Message> respMsgs;
    private FileRecord reqFile;
    private FileRecord respFile;
    private Date createTime;
    private Long totalCost;
    @Indexed(name = "idx_apiName")
    private String apiName;

    @AllArgsConstructor
    @Data
    public static class Message {
        private Long ts;
        private String msg;
    }

    @AllArgsConstructor
    @Data
    public static class FileRecord {
        private Long ts;
        private Integer size;
        private String type;
        private String url;
    }
}
