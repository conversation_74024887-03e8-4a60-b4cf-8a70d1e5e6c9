package cn.iocoder.yudao.module.iot.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "baidu.asr")
@Slf4j
public class BaiduAsrProperties {
    private  String appid="gNCcuuUkSB63XXEK8Q1ljjPSBrxAqyPY";
    private  String appkey="GDZlYoLrLZ1jQCBq8W7jKS6aD0rxdB74";
    private  String pid="ne9U0";
    private  String serverUrl="ws://duer-kids.baidu.com/sandbox/sota/realtime_asr";
    private String insideRcUrl="https://openapi-iot.baidu.com/sandbox/inside-rc/chat";
    private String noteSummaryurl="https://openapi-iot.baidu.com/sandbox/inside-rc/note/summary";
    private String laikuProductId="1899007766144614401";
    private String laikuAppId="SeTT5zWoA5nIvERLb8XgQm20IxxF1KVD";
    private String laikuAppKey="zrvBwHNq6kbJipfzTXcmVwvBnwtDgZyy";
    private String laikuVersion="1";

    private String yinlianVersion="5";
    private String yinlianProductId="1905083781762236417";

    private Long longAsrAppid=119057201L;
    private String longAsrAppkey="X232KFnA2yRYSjYzyYBjokiB";
    private String longAsrAppSecret="l6seIy8TZJPNJM8Ph6NgHtzoikMwCwkM";
    private String longAsrURI="ws://vop.baidu.com/realtime_asr";
}