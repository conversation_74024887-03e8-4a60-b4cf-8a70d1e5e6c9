package cn.iocoder.yudao.module.iot.dal.mongo;

import cn.iocoder.yudao.module.iot.dal.dataobject.device.DeviceDO;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @email: <EMAIL>
 * @date: 2022/7/7 17:38
 */
@Repository
public interface DeviceRepository extends MongoRepository<DeviceDO, Long> {
    Optional<DeviceDO> findFirstByProductKeyAndSn(String productKey, String sn);
    Optional<DeviceDO> findFirstByProductKeyAndSnCode(String productKey, String snCode);
    List<DeviceDO> findAllBySnIsNull();

    Optional<DeviceDO> findBySn(String sn);
    Optional<DeviceDO> findFirstBySn(String sn);
    Optional<DeviceDO> findByMac(String mac);
}
